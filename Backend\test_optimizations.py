#!/usr/bin/env python3
"""
Test script to verify MCP bot optimizations are working
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the Backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import Configuration, ChatSession, BedrockClient

async def test_optimizations():
    """Test the optimization features"""
    print("🚀 Testing MCP Bot Optimizations...")
    
    # Test Configuration
    print("\n1. Testing Configuration...")
    config = Configuration()
    print(f"   ✓ Model: {config.model_id}")
    print(f"   ✓ Max iterations: {config.max_tool_iterations}")
    print(f"   ✓ Response size limit: {config.response_size_limit}")
    print(f"   ✓ Temperature: {config.temperature}")
    print(f"   ✓ Max tokens: {config.max_tokens}")
    
    # Test BedrockClient
    print("\n2. Testing BedrockClient...")
    try:
        bedrock = BedrockClient(config.model_id, config.region, config.aws_profile, config)
        print("   ✓ BedrockClient initialized")
    except Exception as e:
        print(f"   ❌ BedrockClient failed: {e}")
        return False
    
    # Test ChatSession
    print("\n3. Testing ChatSession...")
    try:
        chat_session = ChatSession(bedrock, config)
        print("   ✓ ChatSession initialized")
        
        # Test optimization methods
        if hasattr(chat_session, 'should_continue_tools'):
            print("   ✓ should_continue_tools method available")
        else:
            print("   ❌ should_continue_tools method missing")
            
        if hasattr(chat_session, '_has_sufficient_data'):
            print("   ✓ _has_sufficient_data method available")
        else:
            print("   ❌ _has_sufficient_data method missing")
            
        if hasattr(chat_session, '_preprocess_query'):
            print("   ✓ _preprocess_query method available")
        else:
            print("   ❌ _preprocess_query method missing")
            
    except Exception as e:
        print(f"   ❌ ChatSession failed: {e}")
        return False
    
    # Test enhanced query preprocessing
    print("\n4. Testing Enhanced Query Preprocessing...")
    try:
        test_queries = [
            "What is AWS EC2?",  # General query
            "How much does EC2 cost?",  # Pricing query
            "Compare AWS vs Azure pricing",  # Comparison query
            "What will it cost to build a web application on AWS?",  # Project estimate
            "Bedrock pricing in ap-south-1 region",  # Specific pricing query
            "Compare EC2 pricing between us-east-1 and eu-west-1",  # Regional comparison
        ]

        for query in test_queries:
            analysis = chat_session._preprocess_query(query)
            print(f"   Query: '{query}'")
            print(f"   Analysis: {analysis}")
            print(f"   Requires multiple iterations: {analysis.get('requires_multiple_iterations', False)}")
            print()

    except Exception as e:
        print(f"   ❌ Query preprocessing failed: {e}")
        return False
    
    # Test enhanced smart stopping conditions
    print("\n5. Testing Enhanced Smart Stopping...")
    try:
        # Test with no executions
        should_continue = chat_session.should_continue_tools(0, [])
        print(f"   ✓ No executions: should_continue = {should_continue}")

        # Test with successful execution (non-pricing)
        mock_executions = [{
            'status': 'success',
            'result': 'This is a successful result with enough data to answer the query completely.'
        }]
        should_continue = chat_session.should_continue_tools(0, mock_executions, text_content="", query="What is AWS EC2?")
        print(f"   ✓ Successful execution (general): should_continue = {should_continue}")

        # Test with pricing query
        pricing_executions = [{
            'status': 'success',
            'tool_name': 'get_pricing',
            'result': '{"pricePerUnit": {"USD": "0.0116"}, "description": "EC2 pricing"}'
        }]
        should_continue = chat_session.should_continue_tools(1, pricing_executions, text_content="", query="How much does EC2 cost?")
        print(f"   ✓ Pricing query with data: should_continue = {should_continue}")

        # Test max iterations for pricing vs general
        should_continue_general = chat_session.should_continue_tools(4, [], text_content="", query="What is AWS?")
        should_continue_pricing = chat_session.should_continue_tools(4, [], text_content="", query="How much does Bedrock cost?")
        print(f"   ✓ Max iterations general (4): should_continue = {should_continue_general}")
        print(f"   ✓ Max iterations pricing (4): should_continue = {should_continue_pricing}")

    except Exception as e:
        print(f"   ❌ Smart stopping failed: {e}")
        return False
    
    # Test new data management features
    print("\n6. Testing Data Management Optimizations...")
    try:
        # Test query decomposition detection
        complex_queries = [
            "Show me all Bedrock pricing",
            "Compare EC2 pricing between all regions",
            "Complete AWS pricing breakdown"
        ]

        for query in complex_queries:
            analysis = chat_session._preprocess_query(query)
            requires_decomp = analysis.get('requires_decomposition', False)
            print(f"   Query: '{query}' → Requires decomposition: {requires_decomp}")

        # Test parameter optimization
        test_params = {
            "service_code": "AmazonBedrock",
            "region": "us-east-1"
        }
        optimized = chat_session._optimize_pricing_parameters(test_params)
        print(f"   ✓ Parameter optimization: {optimized.get('output_options', {})}")

        # Test response size estimation
        estimated_size = chat_session._estimate_response_size(test_params)
        print(f"   ✓ Response size estimation: {estimated_size} chars")

    except Exception as e:
        print(f"   ❌ Data management tests failed: {e}")
        return False

    print("\n✅ All optimization tests passed!")
    print("\n📋 Complete Optimization Summary:")
    print("   • Temperature increased to 0.8 for better reasoning")
    print("   • Max tool iterations: 4 general, 6 for pricing queries")
    print("   • Enhanced smart stopping with pricing query awareness")
    print("   • Advanced query preprocessing with project cost detection")
    print("   • Pricing-optimized response size handling (45KB for pricing)")
    print("   • Multi-iteration support for complex pricing analysis")
    print("   • Regional pricing comparison capabilities")
    print("   • Project cost estimation support")
    print("   • 🆕 Query decomposition for complex requests")
    print("   • 🆕 Progressive parameter refinement")
    print("   • 🆕 Response size prediction")
    print("   • 🆕 Smart fallback mechanisms")
    print("   • 🆕 Result_too_large error prevention")
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(test_optimizations())
        if result:
            print("\n🎉 MCP Bot is optimized and ready!")
            sys.exit(0)
        else:
            print("\n❌ Optimization tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)
