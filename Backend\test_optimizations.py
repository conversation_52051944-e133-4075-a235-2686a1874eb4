#!/usr/bin/env python3
"""
Test script to verify MCP bot optimizations are working
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the Backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import Configuration, ChatSession, BedrockClient

async def test_optimizations():
    """Test the optimization features"""
    print("🚀 Testing MCP Bot Optimizations...")
    
    # Test Configuration
    print("\n1. Testing Configuration...")
    config = Configuration()
    print(f"   ✓ Model: {config.model_id}")
    print(f"   ✓ Max iterations: {config.max_tool_iterations}")
    print(f"   ✓ Response size limit: {config.response_size_limit}")
    print(f"   ✓ Temperature: {config.temperature}")
    print(f"   ✓ Max tokens: {config.max_tokens}")
    
    # Test BedrockClient
    print("\n2. Testing BedrockClient...")
    try:
        bedrock = BedrockClient(config.model_id, config.region, config.aws_profile, config)
        print("   ✓ BedrockClient initialized")
    except Exception as e:
        print(f"   ❌ BedrockClient failed: {e}")
        return False
    
    # Test ChatSession
    print("\n3. Testing ChatSession...")
    try:
        chat_session = ChatSession(bedrock, config)
        print("   ✓ ChatSession initialized")
        
        # Test optimization methods
        if hasattr(chat_session, 'should_continue_tools'):
            print("   ✓ should_continue_tools method available")
        else:
            print("   ❌ should_continue_tools method missing")
            
        if hasattr(chat_session, '_has_sufficient_data'):
            print("   ✓ _has_sufficient_data method available")
        else:
            print("   ❌ _has_sufficient_data method missing")
            
        if hasattr(chat_session, '_preprocess_query'):
            print("   ✓ _preprocess_query method available")
        else:
            print("   ❌ _preprocess_query method missing")
            
    except Exception as e:
        print(f"   ❌ ChatSession failed: {e}")
        return False
    
    # Test query preprocessing
    print("\n4. Testing Query Preprocessing...")
    try:
        test_queries = [
            "What is AWS EC2?",  # General query
            "How much does EC2 cost?",  # Pricing query
            "Compare AWS vs Azure pricing",  # Comparison query
        ]
        
        for query in test_queries:
            analysis = chat_session._preprocess_query(query)
            print(f"   Query: '{query}'")
            print(f"   Analysis: {analysis}")
            
    except Exception as e:
        print(f"   ❌ Query preprocessing failed: {e}")
        return False
    
    # Test smart stopping conditions
    print("\n5. Testing Smart Stopping...")
    try:
        # Test with no executions
        should_continue = chat_session.should_continue_tools(0, [], "")
        print(f"   ✓ No executions: should_continue = {should_continue}")
        
        # Test with successful execution
        mock_executions = [{
            'status': 'success',
            'result': 'This is a successful result with enough data to answer the query completely.'
        }]
        should_continue = chat_session.should_continue_tools(0, mock_executions, "")
        print(f"   ✓ Successful execution: should_continue = {should_continue}")
        
        # Test with max iterations
        should_continue = chat_session.should_continue_tools(2, [], "")
        print(f"   ✓ Max iterations: should_continue = {should_continue}")
        
    except Exception as e:
        print(f"   ❌ Smart stopping failed: {e}")
        return False
    
    print("\n✅ All optimization tests passed!")
    print("\n📋 Optimization Summary:")
    print("   • Max tool iterations reduced to 2")
    print("   • Smart stopping conditions implemented")
    print("   • Query preprocessing enabled")
    print("   • Response size limits optimized")
    print("   • Nova Lite model configured")
    print("   • Progressive error handling")
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(test_optimizations())
        if result:
            print("\n🎉 MCP Bot is optimized and ready!")
            sys.exit(0)
        else:
            print("\n❌ Optimization tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)
