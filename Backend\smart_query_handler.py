"""
Smart Query Handler for MCP Bot
Implements progressive parameter discovery and validation to prevent tool execution waste
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class QueryStrategy:
    """Defines a strategy for executing a complex query"""
    name: str
    steps: List[Dict[str, Any]]
    max_attempts: int = 2
    fallback_strategy: Optional[str] = None

class SmartQueryHandler:
    """
    Handles complex queries with progressive parameter discovery and validation
    """
    
    def __init__(self):
        self.strategies = self._initialize_strategies()
        self.parameter_cache = {}
        
    def _initialize_strategies(self) -> Dict[str, QueryStrategy]:
        """Initialize predefined query strategies"""
        return {
            "s3_regional_pricing": QueryStrategy(
                name="S3 Regional Pricing Comparison",
                steps=[
                    {
                        "tool": "get_pricing_service_attributes",
                        "args": {"service_code": "AmazonS3"},
                        "purpose": "Discover available S3 pricing attributes"
                    },
                    {
                        "tool": "get_pricing_attribute_values", 
                        "args": {"service_code": "AmazonS3", "attribute_name": "usagetype"},
                        "purpose": "Get valid S3 usage types"
                    },
                    {
                        "tool": "get_pricing",
                        "args": {
                            "service_code": "AmazonS3",
                            "region": "{target_region}",
                            "filters": [{"Field": "usagetype", "Type": "EQUALS", "Value": "{discovered_usage_type}"}],
                            "output_options": {"pricing_terms": ["OnDemand"]}
                        },
                        "purpose": "Get pricing for specific region and usage type"
                    }
                ],
                max_attempts=2,
                fallback_strategy="simple_s3_pricing"
            ),
            
            "simple_s3_pricing": QueryStrategy(
                name="Simple S3 Pricing",
                steps=[
                    {
                        "tool": "get_pricing",
                        "args": {
                            "service_code": "AmazonS3",
                            "region": "{target_region}",
                            "output_options": {"pricing_terms": ["OnDemand"]},
                            "max_results": 20
                        },
                        "purpose": "Get basic S3 pricing without complex filters"
                    }
                ],
                max_attempts=1
            ),
            
            "ec2_instance_pricing": QueryStrategy(
                name="EC2 Instance Pricing",
                steps=[
                    {
                        "tool": "get_pricing_attribute_values",
                        "args": {"service_code": "AmazonEC2", "attribute_name": "instanceType"},
                        "purpose": "Validate instance type exists"
                    },
                    {
                        "tool": "get_pricing",
                        "args": {
                            "service_code": "AmazonEC2",
                            "region": "{target_region}",
                            "filters": [
                                {"Field": "instanceType", "Type": "EQUALS", "Value": "{instance_type}"},
                                {"Field": "tenancy", "Type": "EQUALS", "Value": "Shared"},
                                {"Field": "operating-system", "Type": "EQUALS", "Value": "Linux"}
                            ],
                            "output_options": {"pricing_terms": ["OnDemand"]}
                        },
                        "purpose": "Get EC2 pricing with validated parameters"
                    }
                ],
                max_attempts=2
            )
        }
    
    def detect_query_type(self, user_message: str) -> str:
        """Detect the type of query based on user message"""
        message_lower = user_message.lower()
        
        if "s3" in message_lower and ("region" in message_lower or "lowest" in message_lower or "compare" in message_lower):
            return "s3_regional_pricing"
        elif "ec2" in message_lower and ("instance" in message_lower or "pricing" in message_lower):
            return "ec2_instance_pricing"
        else:
            return "generic"
    
    def extract_parameters(self, user_message: str, query_type: str) -> Dict[str, Any]:
        """Extract parameters from user message based on query type"""
        params = {}
        message_lower = user_message.lower()
        
        if query_type == "s3_regional_pricing":
            # Extract regions mentioned in the message
            regions = []
            region_keywords = {
                "us-east-1": ["us-east-1", "virginia", "n. virginia"],
                "us-west-2": ["us-west-2", "oregon"],
                "eu-west-1": ["eu-west-1", "ireland"],
                "ap-southeast-1": ["ap-southeast-1", "singapore"]
            }
            
            for region_code, keywords in region_keywords.items():
                if any(keyword in message_lower for keyword in keywords):
                    regions.append(region_code)
            
            if not regions:
                # Default to common regions for comparison
                regions = ["us-east-1", "us-west-2", "eu-west-1"]
            
            params["target_regions"] = regions
            
        elif query_type == "ec2_instance_pricing":
            # Extract instance type
            import re
            instance_match = re.search(r'(t[2-4]\.[a-z]+|m[4-6]\.[a-z]+|c[4-6]\.[a-z]+)', message_lower)
            if instance_match:
                params["instance_type"] = instance_match.group(1)
            
            # Extract region
            region_match = re.search(r'(us-[a-z]+-\d+|eu-[a-z]+-\d+|ap-[a-z]+-\d+)', message_lower)
            if region_match:
                params["target_region"] = region_match.group(1)
            else:
                params["target_region"] = "us-east-1"  # Default
        
        return params
    
    def validate_parameters(self, strategy: QueryStrategy, params: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate parameters before execution"""
        errors = []
        
        # Basic validation rules
        if "target_region" in params:
            valid_regions = [
                "us-east-1", "us-east-2", "us-west-1", "us-west-2",
                "eu-west-1", "eu-west-2", "eu-central-1",
                "ap-southeast-1", "ap-southeast-2", "ap-northeast-1"
            ]
            if params["target_region"] not in valid_regions:
                errors.append(f"Invalid region: {params['target_region']}")
        
        if "instance_type" in params:
            # Basic instance type validation
            import re
            if not re.match(r'^[a-z]\d+\.[a-z]+$', params["instance_type"]):
                errors.append(f"Invalid instance type format: {params['instance_type']}")
        
        return len(errors) == 0, errors
    
    def create_optimized_query_plan(self, user_message: str) -> Dict[str, Any]:
        """Create an optimized query execution plan"""
        query_type = self.detect_query_type(user_message)
        params = self.extract_parameters(user_message, query_type)
        
        if query_type in self.strategies:
            strategy = self.strategies[query_type]
            is_valid, errors = self.validate_parameters(strategy, params)
            
            if not is_valid:
                logger.warning(f"Parameter validation failed: {errors}")
                # Fall back to simpler strategy
                if strategy.fallback_strategy:
                    strategy = self.strategies[strategy.fallback_strategy]
            
            return {
                "strategy": strategy,
                "parameters": params,
                "validation_errors": errors if not is_valid else [],
                "estimated_tool_calls": len(strategy.steps)
            }
        
        return {
            "strategy": None,
            "parameters": params,
            "validation_errors": ["No specific strategy found for this query type"],
            "estimated_tool_calls": 1
        }
    
    def should_use_progressive_discovery(self, user_message: str) -> bool:
        """Determine if progressive discovery should be used"""
        complex_indicators = [
            "compare", "lowest", "cheapest", "across regions",
            "multiple", "all regions", "best price"
        ]
        
        return any(indicator in user_message.lower() for indicator in complex_indicators)

# Usage example for integration
def get_query_recommendations(user_message: str) -> Dict[str, Any]:
    """Get recommendations for optimizing a user query"""
    handler = SmartQueryHandler()
    plan = handler.create_optimized_query_plan(user_message)
    
    recommendations = {
        "use_progressive_discovery": handler.should_use_progressive_discovery(user_message),
        "estimated_tool_calls": plan["estimated_tool_calls"],
        "strategy_name": plan["strategy"].name if plan["strategy"] else "Generic",
        "validation_errors": plan["validation_errors"],
        "parameters": plan["parameters"]
    }
    
    return recommendations
