{"mcpServers": {"aws-pricing": {"command": "python", "args": ["-m", "awslabs.aws_pricing_mcp_server.server"], "env": {"AWS_REGION": "ap-south-1", "AWS_PROFILE": "default", "MAX_RESULTS": "50", "DEFAULT_PRICING_TERMS": "OnDemand", "RESPONSE_SIZE_LIMIT": "80000"}, "cwd": "../mcp/src/aws-pricing-mcp-server", "description": "AWS Pricing API server for cost analysis"}, "cost-explorer": {"command": "python", "args": ["-m", "awslabs.cost_explorer_mcp_server.server"], "env": {"AWS_REGION": "ap-south-1", "AWS_PROFILE": "default", "DEFAULT_GRANULARITY": "MONTHLY"}, "cwd": "../mcp/src/cost-explorer-mcp-server", "description": "AWS Cost Explorer API server for analyzing actual AWS spending, usage patterns, and cost trends"}}}