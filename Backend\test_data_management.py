#!/usr/bin/env python3
"""
Comprehensive test for data management optimizations
Tests all the new features to handle "result_too_large" issues
"""

import asyncio
import json
import sys
import os
from datetime import datetime

# Add the Backend directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from main import Configuration, ChatSession, BedrockClient

async def test_data_management_optimizations():
    """Test all data management optimization features"""
    print("🔧 Testing Data Management Optimizations...")
    
    # Initialize components
    config = Configuration()
    bedrock = BedrockClient(config.model_id, config.region, config.aws_profile, config)
    chat_session = ChatSession(bedrock, config)
    
    print(f"✓ Configuration: Max chunk size: {config.max_chunk_size}")
    print(f"✓ Configuration: Pricing result limit: {config.pricing_result_limit}")
    
    # Test 1: Query Decomposition Detection
    print("\n1. Testing Query Decomposition Detection...")
    test_queries = [
        ("Show me all Bedrock pricing", True),
        ("Complete AWS pricing breakdown", True),
        ("Compare EC2 pricing between all regions", True),
        ("Bedrock Nova Lite pricing in us-east-1", False),
        ("What is the cost of t3.micro instance?", False)
    ]
    
    for query, should_decompose in test_queries:
        analysis = chat_session._preprocess_query(query)
        requires_decomp = analysis.get('requires_decomposition', False)
        status = "✓" if requires_decomp == should_decompose else "❌"
        print(f"   {status} '{query}' → Decompose: {requires_decomp} (expected: {should_decompose})")
    
    # Test 2: Query Decomposition Logic
    print("\n2. Testing Query Decomposition Logic...")
    complex_queries = [
        "Show me all Bedrock pricing",
        "Compare EC2 pricing between us-east-1 and eu-west-1",
        "Complete pricing for all AWS services"
    ]
    
    for query in complex_queries:
        chunks = chat_session._decompose_pricing_query(query)
        print(f"   Query: '{query}'")
        print(f"   Chunks: {len(chunks)}")
        for i, chunk in enumerate(chunks):
            print(f"     {i+1}. {chunk.get('description', 'No description')}")
    
    # Test 3: Parameter Optimization
    print("\n3. Testing Parameter Optimization...")
    test_params = [
        {"service_code": "AmazonBedrock"},
        {"service_code": "AmazonEC2", "region": "us-east-1"},
        {"service_code": "AmazonS3", "filters": [{"field": "storageClass", "value": "Standard"}]}
    ]
    
    for params in test_params:
        optimized = chat_session._optimize_pricing_parameters(params)
        print(f"   Original: {params}")
        print(f"   Optimized: {optimized}")
        print(f"   Added limits: {optimized.get('max_results', 'None')}")
        print(f"   Added terms: {optimized.get('output_options', {}).get('pricing_terms', 'None')}")
        print()
    
    # Test 4: Response Size Estimation
    print("\n4. Testing Response Size Estimation...")
    size_test_params = [
        {"services": ["bedrock"], "regions": ["us-east-1"]},
        {"services": ["ec2", "s3", "lambda"], "regions": ["us-east-1", "eu-west-1"]},
        {"services": ["bedrock"], "regions": ["us-east-1"], "features": ["inference"], "pricing_terms": ["OnDemand"]}
    ]
    
    for params in size_test_params:
        estimated_size = chat_session._estimate_response_size(params)
        status = "Large (needs decomposition)" if estimated_size == -1 else f"{estimated_size} chars"
        print(f"   Params: {params}")
        print(f"   Estimated size: {status}")
        print()
    
    # Test 5: Progressive Refinements
    print("\n5. Testing Progressive Refinements...")
    base_params = {"service_code": "AmazonBedrock", "region": "us-east-1"}
    refinements = chat_session._create_progressive_refinements(base_params)
    
    print(f"   Base params: {base_params}")
    print(f"   Generated {len(refinements)} refinement levels:")
    for i, (name, params) in enumerate(refinements):
        print(f"     Level {i+1} ({name}): max_results={params.get('max_results', 'None')}")
    
    # Test 6: Complex Query Detection
    print("\n6. Testing Complex Query Detection...")
    complexity_tests = [
        ({"service_code": "AmazonBedrock"}, True),  # No filters = complex
        ({"service_code": "AmazonBedrock", "filters": [{"field": "model", "value": "nova"}]}, False),  # Has filters
        ({"service_code": "AmazonEC2", "max_results": 10}, False),  # Has limits
        ({"service_code": "AmazonS3"}, True)  # No filters or limits = complex
    ]
    
    for params, expected_complex in complexity_tests:
        is_complex = chat_session._is_complex_pricing_query(params)
        status = "✓" if is_complex == expected_complex else "❌"
        print(f"   {status} {params} → Complex: {is_complex} (expected: {expected_complex})")
    
    # Test 7: Region Extraction
    print("\n7. Testing Region Extraction...")
    region_queries = [
        ("Compare pricing between us-east-1 and eu-west-1", ["us-east-1", "eu-west-1"]),
        ("Bedrock pricing in ap-south-1 region", ["ap-south-1"]),
        ("Virginia vs Oregon pricing", ["us-east-1", "us-west-2"]),
        ("General pricing query", [])
    ]
    
    for query, expected_regions in region_queries:
        extracted = chat_session._extract_regions_from_query(query)
        status = "✓" if set(extracted) == set(expected_regions) else "❌"
        print(f"   {status} '{query}' → {extracted} (expected: {expected_regions})")
    
    print("\n✅ All data management optimization tests completed!")
    print("\n📊 Summary of Implemented Solutions:")
    print("   🔍 Query complexity detection")
    print("   ⚡ Automatic query decomposition")
    print("   🎯 Progressive parameter refinement")
    print("   📏 Response size prediction")
    print("   🔄 Smart fallback mechanisms")
    print("   🌍 Regional query optimization")
    print("   📈 Parameter optimization")
    
    return True

if __name__ == "__main__":
    try:
        result = asyncio.run(test_data_management_optimizations())
        if result:
            print("\n🎉 Data management optimizations are working correctly!")
            sys.exit(0)
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)
