"""
Query Interceptor for MCP Bot
Prevents common query failures and provides intelligent fallbacks
"""

import logging
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass

logger = logging.getLogger(__name__)

@dataclass
class QueryPattern:
    """Defines a problematic query pattern and its solution"""
    pattern: str
    problem: str
    solution: str
    example: str

class QueryInterceptor:
    """
    Intercepts problematic queries and provides intelligent guidance
    """
    
    def __init__(self):
        self.problematic_patterns = self._initialize_patterns()
        self.failure_history = []
        
    def _initialize_patterns(self) -> List[QueryPattern]:
        """Initialize known problematic query patterns"""
        return [
            QueryPattern(
                pattern=r"difference.*between.*(t\d+\.\w+).*and.*(c\d+\.\w+)",
                problem="Trying to compare multiple instance types in one query",
                solution="Query each instance type separately, then compare results",
                example="First: Get t3.large pricing. Then: Get c5.large pricing. Finally: Compare the results."
            ),
            QueryPattern(
                pattern=r"(s3|ec2).*pricing.*(region|all)",
                problem="Broad queries return too much data",
                solution="Use specific filters and query one region at a time",
                example="Add filters like storageClass=Standard or instanceType=t3.medium"
            ),
            QueryPattern(
                pattern=r"lowest.*cost.*(region|across)",
                problem="Multi-region comparison requires multiple queries",
                solution="Query 3-4 key regions individually, then compare",
                example="Query us-east-1, us-west-2, eu-west-1 separately"
            ),
            QueryPattern(
                pattern=r"all.*(pricing|costs|instances)",
                problem="Requesting all data causes result_too_large errors",
                solution="Use specific filters to narrow the scope",
                example="Instead of 'all EC2', use 'EC2 t3.medium instances'"
            )
        ]
    
    def analyze_query(self, user_message: str) -> Dict[str, Any]:
        """Analyze a user query for potential problems"""
        message_lower = user_message.lower()
        
        analysis = {
            "is_problematic": False,
            "problems": [],
            "solutions": [],
            "risk_level": "low",
            "suggested_approach": None
        }
        
        # Check against known problematic patterns
        for pattern in self.problematic_patterns:
            if re.search(pattern.pattern, message_lower):
                analysis["is_problematic"] = True
                analysis["problems"].append(pattern.problem)
                analysis["solutions"].append(pattern.solution)
                analysis["risk_level"] = "high"
        
        # Analyze query complexity
        complexity_indicators = [
            "compare", "difference", "between", "all", "every", 
            "across", "multiple", "various", "different"
        ]
        
        complexity_score = sum(1 for indicator in complexity_indicators if indicator in message_lower)
        
        if complexity_score >= 2:
            analysis["risk_level"] = "medium" if analysis["risk_level"] == "low" else "high"
            analysis["problems"].append("High complexity query detected")
            analysis["solutions"].append("Break down into simpler, sequential queries")
        
        # Generate suggested approach
        analysis["suggested_approach"] = self._generate_approach(user_message, analysis)
        
        return analysis
    
    def _generate_approach(self, user_message: str, analysis: Dict[str, Any]) -> Optional[str]:
        """Generate a suggested approach for the query"""
        message_lower = user_message.lower()
        
        # Instance comparison queries
        if "difference" in message_lower and ("instance" in message_lower or "ec2" in message_lower):
            instances = re.findall(r'(t\d+\.\w+|c\d+\.\w+|m\d+\.\w+|r\d+\.\w+)', message_lower)
            if len(instances) >= 2:
                return f"""
SUGGESTED APPROACH:
1. Query {instances[0]} pricing with specific filters
2. Query {instances[1]} pricing with same filters  
3. Compare the results
4. Use filters: instanceType, tenancy=Shared, operating-system=Linux
"""
        
        # Regional comparison queries
        if "region" in message_lower and ("lowest" in message_lower or "compare" in message_lower):
            return """
SUGGESTED APPROACH:
1. Use get_pricing_attribute_values to find valid usage types
2. Query 3-4 key regions individually (us-east-1, us-west-2, eu-west-1)
3. Use specific filters to reduce response size
4. Compare results manually
"""
        
        # Broad service queries
        if any(service in message_lower for service in ["s3", "ec2", "rds"]) and "pricing" in message_lower:
            return """
SUGGESTED APPROACH:
1. Use get_pricing_service_attributes to see available filters
2. Add specific filters (instanceType, storageClass, etc.)
3. Use output_options={"pricing_terms": ["OnDemand"]}
4. Add max_results=10 to limit response size
"""
        
        return None
    
    def should_intercept(self, user_message: str) -> Tuple[bool, str]:
        """Determine if a query should be intercepted"""
        analysis = self.analyze_query(user_message)
        
        if analysis["risk_level"] == "high":
            problems = "; ".join(analysis["problems"])
            solutions = "; ".join(analysis["solutions"])
            
            intercept_message = f"""
⚠️ QUERY OPTIMIZATION REQUIRED

DETECTED ISSUES: {problems}

RECOMMENDED SOLUTIONS: {solutions}

{analysis.get('suggested_approach', '')}

Please rephrase your query to be more specific, or I can help you break it down into smaller parts.
"""
            return True, intercept_message
        
        return False, ""
    
    def get_smart_guidance(self, user_message: str) -> str:
        """Get smart guidance for query optimization"""
        analysis = self.analyze_query(user_message)
        
        if not analysis["is_problematic"]:
            return ""
        
        guidance = "\n\n🎯 SMART QUERY GUIDANCE:\n"
        
        # Add specific guidance based on query type
        message_lower = user_message.lower()
        
        if "difference" in message_lower and "instance" in message_lower:
            guidance += """
For instance comparisons:
1. Query each instance type separately
2. Use identical filters for fair comparison
3. Compare pricing per hour or month
4. Consider performance differences too

Example filters to use:
- instanceType: t3.large (then c5.large separately)
- tenancy: Shared
- operating-system: Linux
- region: us-east-1
"""
        
        elif "s3" in message_lower and ("region" in message_lower or "pricing" in message_lower):
            guidance += """
For S3 pricing queries:
1. Use specific storage class filters
2. Query regions one at a time
3. Focus on standard storage first

Example filters to use:
- storageClass: Standard
- usagetype: TimedStorage-ByteHrs
- region: us-east-1 (then other regions separately)
"""
        
        elif "ec2" in message_lower and "pricing" in message_lower:
            guidance += """
For EC2 pricing queries:
1. Always specify instance type
2. Add tenancy and OS filters
3. Use correct filter syntax
4. Limit results to avoid large responses

CRITICAL FILTER SYNTAX:
- Use "Type": "EQUALS" (not "equals")
- Use "Field": "instanceType", "Type": "EQUALS", "Value": "t3.large"
- Use "Field": "tenancy", "Type": "EQUALS", "Value": "Shared"
- Use "Field": "operating-system", "Type": "EQUALS", "Value": "Linux"

Example complete filter set:
[
  {"Field": "instanceType", "Type": "EQUALS", "Value": "t3.large"},
  {"Field": "tenancy", "Type": "EQUALS", "Value": "Shared"},
  {"Field": "operating-system", "Type": "EQUALS", "Value": "Linux"}
]
"""
        
        return guidance

# Global interceptor instance
query_interceptor = QueryInterceptor()

def intercept_query(user_message: str) -> Tuple[bool, str, str]:
    """
    Intercept and analyze a user query
    Returns: (should_intercept, intercept_message, guidance)
    """
    should_intercept, intercept_message = query_interceptor.should_intercept(user_message)
    guidance = query_interceptor.get_smart_guidance(user_message)
    
    return should_intercept, intercept_message, guidance
