"""
Tool Execution Optimizer for MCP Bot
Prevents inefficient tool usage patterns and implements smart retry logic
"""

import json
import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)

@dataclass
class ToolExecutionResult:
    """Result of a tool execution attempt"""
    success: bool
    result: Any
    error_type: Optional[str] = None
    error_message: Optional[str] = None
    execution_time: float = 0.0
    tokens_used: int = 0

@dataclass
class ToolExecutionPattern:
    """Pattern for detecting inefficient tool usage"""
    tool_name: str
    max_consecutive_failures: int = 3
    cooldown_period: timedelta = timedelta(seconds=30)
    similar_args_threshold: float = 0.8

class ToolExecutionOptimizer:
    """
    Optimizes tool execution to prevent waste and improve success rates
    """
    
    def __init__(self, max_tool_executions: int = 5):
        self.max_tool_executions = max_tool_executions
        self.execution_history: List[Dict[str, Any]] = []
        self.failure_patterns: Dict[str, List[datetime]] = {}
        self.parameter_cache: Dict[str, Any] = {}
        
    def should_execute_tool(self, tool_name: str, args: Dict[str, Any]) -> Tuple[bool, str]:
        """
        Determine if a tool should be executed based on history and patterns
        """
        # Check if we've hit the maximum execution limit
        if len(self.execution_history) >= self.max_tool_executions:
            return False, f"Maximum tool executions ({self.max_tool_executions}) reached"
        
        # Check for recent similar failures
        similar_failures = self._find_similar_recent_failures(tool_name, args)
        if len(similar_failures) >= 2:
            return False, f"Too many similar failures for {tool_name} with similar parameters"
        
        # Check for rapid consecutive failures
        recent_failures = self._get_recent_failures(tool_name, minutes=5)
        if len(recent_failures) >= 3:
            return False, f"Too many recent failures for {tool_name} (rate limiting)"
        
        # Check for parameter validation issues
        validation_result = self._validate_parameters(tool_name, args)
        if not validation_result[0]:
            return False, f"Parameter validation failed: {validation_result[1]}"
        
        return True, "Execution approved"
    
    def _find_similar_recent_failures(self, tool_name: str, args: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Find recent failures with similar parameters"""
        similar_failures = []
        current_time = datetime.now()
        
        for execution in self.execution_history:
            if (execution.get("tool_name") == tool_name and 
                not execution.get("success", False) and
                (current_time - execution.get("timestamp", current_time)).total_seconds() < 300):  # 5 minutes
                
                similarity = self._calculate_parameter_similarity(args, execution.get("args", {}))
                if similarity > 0.7:  # 70% similarity threshold
                    similar_failures.append(execution)
        
        return similar_failures
    
    def _get_recent_failures(self, tool_name: str, minutes: int = 5) -> List[Dict[str, Any]]:
        """Get recent failures for a specific tool"""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        return [
            execution for execution in self.execution_history
            if (execution.get("tool_name") == tool_name and
                not execution.get("success", False) and
                execution.get("timestamp", datetime.min) > cutoff_time)
        ]
    
    def _calculate_parameter_similarity(self, args1: Dict[str, Any], args2: Dict[str, Any]) -> float:
        """Calculate similarity between two parameter sets"""
        if not args1 and not args2:
            return 1.0
        if not args1 or not args2:
            return 0.0
        
        # Simple similarity calculation based on matching keys and values
        all_keys = set(args1.keys()) | set(args2.keys())
        if not all_keys:
            return 1.0
        
        matching_keys = 0
        for key in all_keys:
            if key in args1 and key in args2:
                if args1[key] == args2[key]:
                    matching_keys += 1
                elif isinstance(args1[key], (list, dict)) and isinstance(args2[key], (list, dict)):
                    # For complex types, check if they're similar
                    if str(args1[key]) == str(args2[key]):
                        matching_keys += 1
                    else:
                        matching_keys += 0.5  # Partial match
        
        return matching_keys / len(all_keys)
    
    def _validate_parameters(self, tool_name: str, args: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate parameters before execution"""
        
        if tool_name == "get_pricing":
            return self._validate_pricing_parameters(args)
        elif tool_name == "get_cost_and_usage":
            return self._validate_cost_explorer_parameters(args)
        
        return True, "No specific validation rules"
    
    def _validate_pricing_parameters(self, args: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate AWS Pricing API parameters"""

        # Check service code
        service_code = args.get("service_code")
        if not service_code:
            return False, "Missing required parameter: service_code"

        # Validate region format
        region = args.get("region")
        if region:
            if isinstance(region, list):
                return False, "Region should be a single string, not a list. Query regions individually."

            if not isinstance(region, str) or not region.strip():
                return False, "Region must be a non-empty string"

            # Basic region format validation
            import re
            if not re.match(r'^[a-z]{2}-[a-z]+-\d+$', region):
                return False, f"Invalid region format: {region}. Expected format: us-east-1"

        # Validate filters
        filters = args.get("filters", [])
        if filters:
            # Check for multiple filters on the same field (logical error)
            field_counts = {}
            for filter_item in filters:
                if not isinstance(filter_item, dict):
                    return False, "Each filter must be a dictionary"

                required_fields = ["Field", "Type", "Value"]
                for field in required_fields:
                    if field not in filter_item:
                        return False, f"Filter missing required field: {field}"

                # Count fields to detect logical errors
                field_name = filter_item["Field"]
                field_counts[field_name] = field_counts.get(field_name, 0) + 1

                # Validate filter type
                valid_types = ["EQUALS", "NOT_EQUALS", "CONTAINS", "BEGINS_WITH", "TERM_MATCH", "TERM_CONTAIN"]
                if filter_item["Type"] not in valid_types:
                    return False, f"Invalid filter type: {filter_item['Type']}. Valid types: {valid_types}"

                # Validate filter value
                if not filter_item["Value"] or filter_item["Value"].strip() == "":
                    return False, f"Filter value cannot be empty for field: {field_name}"

                # Check for invalid characters
                invalid_chars = ["*", "?", "[", "]", "{", "}", "\\"]
                if any(char in str(filter_item["Value"]) for char in invalid_chars):
                    return False, f"Filter value contains invalid characters: {filter_item['Value']}"

            # Check for logical errors (multiple EQUALS filters on same field)
            for field_name, count in field_counts.items():
                if count > 1 and field_name in ["instanceType", "storageClass", "usagetype"]:
                    return False, f"Cannot have multiple EQUALS filters on field '{field_name}'. Query each value separately."

        return True, "Parameters valid"
    
    def _validate_cost_explorer_parameters(self, args: Dict[str, Any]) -> Tuple[bool, str]:
        """Validate AWS Cost Explorer parameters"""
        
        # Check time period
        time_period = args.get("TimePeriod")
        if time_period:
            if not isinstance(time_period, dict):
                return False, "TimePeriod must be a dictionary"
            
            required_fields = ["Start", "End"]
            for field in required_fields:
                if field not in time_period:
                    return False, f"TimePeriod missing required field: {field}"
        
        return True, "Parameters valid"
    
    def record_execution(self, tool_name: str, args: Dict[str, Any], result: ToolExecutionResult):
        """Record a tool execution for pattern analysis"""
        execution_record = {
            "tool_name": tool_name,
            "args": args,
            "success": result.success,
            "error_type": result.error_type,
            "error_message": result.error_message,
            "execution_time": result.execution_time,
            "tokens_used": result.tokens_used,
            "timestamp": datetime.now()
        }
        
        self.execution_history.append(execution_record)
        
        # Update failure patterns
        if not result.success:
            if tool_name not in self.failure_patterns:
                self.failure_patterns[tool_name] = []
            self.failure_patterns[tool_name].append(datetime.now())
    
    def get_optimization_suggestions(self, user_message: str) -> List[str]:
        """Get suggestions for optimizing the current query"""
        suggestions = []

        # Analyze recent failures
        recent_failures = [
            execution for execution in self.execution_history[-5:]
            if not execution.get("success", False)
        ]

        if len(recent_failures) >= 2:
            suggestions.append("Consider using parameter discovery tools first (e.g., get_pricing_service_attributes)")

        # Check for common error patterns
        error_types = [failure.get("error_type") for failure in recent_failures]
        error_messages = [failure.get("error_message", "") for failure in recent_failures]

        if "result_too_large" in error_types:
            # Count consecutive result_too_large errors
            consecutive_large = 0
            for failure in reversed(recent_failures):
                if failure.get("error_type") == "result_too_large":
                    consecutive_large += 1
                else:
                    break

            if consecutive_large >= 2:
                suggestions.append("CRITICAL: Stop repeating the same large query. Use specific filters like instanceType, storageClass, or usagetype")
                suggestions.append("Try: Add filters like {'Field': 'instanceType', 'Type': 'EQUALS', 'Value': 't3.medium'}")
            else:
                suggestions.append("Use more specific filters or limit results with output_options")

        if "api_error" in error_types:
            suggestions.append("Validate parameter formats before making API calls")

            # Check for specific API errors
            for msg in error_messages:
                if "multiple filters" in msg.lower() or "instanceType" in msg:
                    suggestions.append("CRITICAL: Cannot compare multiple instance types in one query. Query each type separately")
                if "PREFIX" in msg or "invalid" in msg.lower():
                    suggestions.append("Use only valid filter types: EQUALS, CONTAINS, BEGINS_WITH")

        if "empty_results" in error_types:
            suggestions.append("Use get_pricing_attribute_values to find valid filter values")

            # Check for logical filter errors
            for msg in error_messages:
                if "multiple" in msg.lower() and "instanceType" in msg:
                    suggestions.append("CRITICAL: Query instance types separately: t3.large first, then c5.large")

        # Query-specific suggestions
        message_lower = user_message.lower()

        if "difference" in message_lower and ("instance" in message_lower or "ec2" in message_lower):
            suggestions.append("For instance comparisons: Query each instance type separately, then compare results")
            suggestions.append("Example: First get t3.large pricing, then get c5.large pricing")

        if "s3" in message_lower and "region" in message_lower:
            suggestions.append("Query regions individually rather than in batches")
            suggestions.append("Use get_pricing_attribute_values to find valid S3 usage types")

        if "ec2" in message_lower and "pricing" in message_lower:
            suggestions.append("Use specific filters: instanceType, tenancy=Shared, operating-system=Linux")
            suggestions.append("Add max_results=10 to limit response size")

        return suggestions
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """Get a summary of tool execution patterns"""
        total_executions = len(self.execution_history)
        successful_executions = sum(1 for ex in self.execution_history if ex.get("success", False))
        
        return {
            "total_executions": total_executions,
            "successful_executions": successful_executions,
            "success_rate": successful_executions / total_executions if total_executions > 0 else 0,
            "remaining_executions": max(0, self.max_tool_executions - total_executions),
            "most_common_errors": self._get_most_common_errors(),
            "optimization_needed": total_executions >= 3 and successful_executions == 0
        }
    
    def _get_most_common_errors(self) -> List[Tuple[str, int]]:
        """Get the most common error types"""
        error_counts = {}
        
        for execution in self.execution_history:
            if not execution.get("success", False):
                error_type = execution.get("error_type", "unknown")
                error_counts[error_type] = error_counts.get(error_type, 0) + 1
        
        return sorted(error_counts.items(), key=lambda x: x[1], reverse=True)

# Integration helper
def create_optimized_tool_execution_plan(user_message: str, available_tools: List[str]) -> Dict[str, Any]:
    """Create an optimized execution plan for a user query"""
    optimizer = ToolExecutionOptimizer()
    
    # Analyze the query
    query_analysis = {
        "requires_discovery": any(keyword in user_message.lower() 
                                for keyword in ["compare", "lowest", "all", "which"]),
        "service_mentioned": None,
        "region_mentioned": None,
        "complexity": "low"
    }
    
    # Detect service
    services = ["s3", "ec2", "rds", "lambda", "dynamodb"]
    for service in services:
        if service in user_message.lower():
            query_analysis["service_mentioned"] = service
            break
    
    # Detect complexity
    if query_analysis["requires_discovery"]:
        query_analysis["complexity"] = "high"
    
    # Create execution plan
    execution_plan = {
        "query_analysis": query_analysis,
        "recommended_approach": "progressive_discovery" if query_analysis["requires_discovery"] else "direct_query",
        "estimated_tool_calls": 3 if query_analysis["requires_discovery"] else 1,
        "optimization_suggestions": optimizer.get_optimization_suggestions(user_message)
    }
    
    return execution_plan
