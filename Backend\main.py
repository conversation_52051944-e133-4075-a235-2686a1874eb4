import shutil
import os
import json
import asyncio
import uuid
import time
from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
from contextlib import AsyncExitStack
from mcp.client.stdio import stdio_client
import boto3
from botocore.exceptions import Client<PERSON>rror, NoCredentialsError
from mcp import ClientSession, StdioServerParameters
import logging
from fastapi import FastAPI, HTTPException, BackgroundTasks
from fastapi.middleware.cors import CORSMiddleware
from contextlib import asynccontextmanager
from pydantic import BaseModel
import uvicorn
from datetime import datetime, timedelta
import threading

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Import optimization modules
try:
    from smart_query_handler import SmartQueryHandler, get_query_recommendations
    from tool_execution_optimizer import ToolExecutionOptimizer, ToolExecutionResult, create_optimized_tool_execution_plan
    from query_interceptor import intercept_query
    OPTIMIZATION_ENABLED = True
    logger.info("Query optimization modules loaded successfully")
except ImportError as e:
    logger.warning(f"Query optimization modules not available: {e}")
    OPTIMIZATION_ENABLED = False

# FastAPI app with lifespan
@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager"""
    # Startup
    global chat_session, is_initialized

    with initialization_lock:
        if is_initialized:
            yield
            return

        try:
            logger.info("Starting application initialization...")

            config = Configuration()

            if not config.model_id:
                raise ValueError("BEDROCK_MODEL_ID environment variable is required")

            # Test AWS credentials and permissions
            try:
                if config.aws_profile == "default":
                    session = boto3.Session()
                else:
                    session = boto3.Session(profile_name=config.aws_profile)

                # Test basic AWS access
                sts = session.client('sts')
                identity = sts.get_caller_identity()
                logger.info(f"AWS identity confirmed: {identity.get('Arn', 'Unknown')}")

                # Test Bedrock permissions
                bedrock_client = session.client('bedrock', region_name=config.region)
                try:
                    # List foundation models to test Bedrock access (using correct parameters)
                    models_response = bedrock_client.list_foundation_models()
                    logger.info(f"Bedrock access confirmed. Found {len(models_response.get('modelSummaries', []))} models")

                    # Check if our model is available
                    available_models = [m['modelId'] for m in models_response.get('modelSummaries', [])]
                    if config.model_id not in available_models:
                        logger.warning(f"Configured model {config.model_id} not found in available models")
                        # Log some available models for reference
                        logger.info(f"Some available models: {available_models[:3]}")

                except ClientError as bedrock_e:
                    if bedrock_e.response['Error']['Code'] == 'AccessDeniedException':
                        logger.warning(f"Bedrock access denied. This may cause model access issues: {bedrock_e.response['Error']['Message']}")
                    else:
                        logger.warning(f"Bedrock permission test failed: {bedrock_e}")

            except Exception as e:
                logger.error(f"AWS credential test failed: {e}")
                raise ValueError(f"AWS credentials not properly configured: {e}")

            # Initialize Bedrock client
            bedrock = BedrockClient(
                model_id=config.model_id,
                region=config.region,
                aws_profile=config.aws_profile,
                config=config
            )

            # Initialize chat session
            chat_session = ChatSession(bedrock, config)
            await chat_session.initialize()

            is_initialized = True
            logger.info("Application initialized successfully")

        except Exception as e:
            logger.error(f"Error during startup: {e}")
            is_initialized = False
            raise

    yield

    # Shutdown
    logger.info("Application shutdown initiated")
    if chat_session:
        try:
            await chat_session.cleanup()
        except Exception as e:
            logger.warning(f"Error cleaning up chat session: {e}")
    logger.info("Application shutdown completed")

app = FastAPI(
    title="AWS Pricing Optimization Agent API",
    version="1.0.0",
    description="AI-powered AWS cost optimization assistant with automatic tool execution",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Global variables
chat_session = None
is_initialized = False
initialization_lock = threading.Lock()

# ------------------------
# Configuration
# ------------------------
class Configuration:
    def __init__(self):
        load_dotenv()
        self.model_id = os.getenv("BEDROCK_MODEL_ID", "amazon.titan-text-lite-v1")
        self.region = os.getenv("AWS_REGION", "us-east-1")
        self.aws_profile = os.getenv("AWS_PROFILE", "default")
        self.max_tokens = int(os.getenv("MAX_TOKENS", "2048"))
        self.temperature = float(os.getenv("TEMPERATURE", "0.8"))

        # Enhanced optimization settings for pricing queries
        self.max_tool_iterations = int(os.getenv("MAX_TOOL_ITERATIONS", "4"))  # Increased for pricing queries
        self.pricing_query_max_iterations = int(os.getenv("PRICING_QUERY_MAX_ITERATIONS", "6"))  # Special limit for pricing
        self.response_size_limit = int(os.getenv("RESPONSE_SIZE_LIMIT", "15000"))
        self.enable_smart_stopping = True
        self.enable_progressive_filtering = True
        self.enable_query_preprocessing = True
        self.enable_pricing_query_optimization = True

        # Bedrock Session Management Configuration
        self.enable_bedrock_sessions = os.getenv("ENABLE_BEDROCK_SESSIONS", "true").lower() == "true"
        self.bedrock_session_fallback = os.getenv("BEDROCK_SESSION_FALLBACK", "true").lower() == "true"
        self.session_retention_days = int(os.getenv("SESSION_RETENTION_DAYS", "7"))

        # Fallback models for different regions
        self.fallback_models = {
            "us-east-1": [
                "anthropic.claude-3-sonnet-20240229-v1:0",
                "anthropic.claude-3-haiku-20240307-v1:0",
                "amazon.titan-text-lite-v1"
            ],
            "ap-south-1": [
                "anthropic.claude-3-sonnet-20240229-v1:0",
                "anthropic.claude-3-haiku-20240307-v1:0",
                "amazon.titan-text-lite-v1"
            ],
            "eu-west-1": [
                "anthropic.claude-3-sonnet-20240229-v1:0",
                "anthropic.claude-3-haiku-20240307-v1:0",
                "amazon.titan-text-lite-v1"
            ]
        }

    @staticmethod
    def load_config(file_path: str) -> dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(file_path, "r") as f:
                return json.load(f)
        except FileNotFoundError:
            logger.warning(f"Configuration file {file_path} not found, creating default")
            return Configuration.create_default_config(file_path)
        except json.JSONDecodeError as e:
            logger.error(f"Error parsing {file_path}: {e}")
            raise

    @staticmethod
    def create_default_config(file_path: str) -> dict[str, Any]:
        """Create default configuration file"""
        default_config = {
            "mcpServers": {
                "aws-pricing": {
                    "command": "npx",
                    "args": ["@modelcontextprotocol/server-aws-pricing"],
                    "env": {},
                    "description": "AWS Pricing API server for cost analysis"
                },
                "calculator": {
                    "command": "npx",
                    "args": ["@modelcontextprotocol/server-calculator"],
                    "env": {},
                    "description": "Calculator server for cost calculations"
                }
            }
        }
        
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, "w") as f:
                json.dump(default_config, f, indent=2)
            logger.info(f"Created default configuration at {file_path}")
        except Exception as e:
            logger.error(f"Failed to create default config: {e}")
            
        return default_config

# ------------------------
# Pydantic Models
# ------------------------
class ChatRequest(BaseModel):
    message: str
    session_id: Optional[str] = None

class ChatResponse(BaseModel):
    response: str
    session_id: str
    tool_executions: List[Dict[str, Any]] = []
    timestamp: str
    tokens_used: Optional[int] = None
    processing_time: float

class ErrorResponse(BaseModel):
    error: str
    details: Optional[str] = None
    timestamp: str

# ------------------------
# Tool Class
# ------------------------
class Tool:
    def __init__(self, name: str, description: str, input_schema: dict[str, Any], server_name: str):
        self.name = name
        self.description = description[:500]  # Limit description length
        self.input_schema = input_schema
        self.server_name = server_name

    def to_bedrock_format(self) -> Dict:
        """Convert tool to Bedrock-compatible format"""
        return {
            "toolSpec": {
                "name": self.name,
                "description": self.description,
                "inputSchema": {
                    "json": {
                        "type": "object",
                        "properties": self.input_schema.get("properties", {}),
                        "required": self.input_schema.get("required", [])
                    }
                },
            }
        }

# ------------------------
# Server Class
# ------------------------
@dataclass
class Server:
    def __init__(self, name: str, config: dict[str, Any]):
        self.name = name
        self.config = config
        self.session: Optional[ClientSession] = None
        self.exit_stack = AsyncExitStack()
        self._initialized = False
        self._max_retries = 3

    async def initialize(self):
        """Initialize MCP server with retry logic"""
        if self._initialized:
            return
            
        for attempt in range(self._max_retries):
            try:
                await self._attempt_initialization()
                self._initialized = True
                logger.info(f"Successfully initialized server: {self.name}")
                return
                
            except Exception as e:
                logger.warning(f"Initialization attempt {attempt + 1} failed for {self.name}: {e}")
                
                if attempt == self._max_retries - 1:
                    logger.error(f"Failed to initialize server {self.name} after {self._max_retries} attempts")
                    await self.cleanup()
                    raise
                
                await asyncio.sleep(2 ** attempt)  # Exponential backoff

    async def _attempt_initialization(self):
        """Single initialization attempt"""
        command = self.config.get("command")
        if command == "npx":
            command = shutil.which("npx")
            if command is None:
                raise ValueError("npx command not found in PATH")

        # Get the working directory from config, resolve relative paths
        cwd = self.config.get("cwd")
        if cwd and not os.path.isabs(cwd):
            # Resolve relative path from the backend directory
            cwd = os.path.abspath(os.path.join(os.path.dirname(__file__), cwd))

        server_params = StdioServerParameters(
            command=command,
            args=self.config.get("args", []),
            env={**os.environ, **self.config.get("env", {})},
            cwd=cwd
        )
        
        stdio_transport = await asyncio.wait_for(
            self.exit_stack.enter_async_context(stdio_client(server_params)),
            timeout=30.0
        )
        
        read, write = stdio_transport
        session = await asyncio.wait_for(
            self.exit_stack.enter_async_context(ClientSession(read, write)),
            timeout=30.0
        )
        
        await asyncio.wait_for(session.initialize(), timeout=30.0)
        self.session = session

    async def list_tools(self) -> List[Tool]:
        """List available tools from this server"""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            tools_response = await asyncio.wait_for(self.session.list_tools(), timeout=15.0)
            tools = []

            # Handle different response formats
            if hasattr(tools_response, 'tools'):
                tools_list = tools_response.tools
            elif isinstance(tools_response, list):
                tools_list = tools_response
            else:
                tools_list = []
                for item in tools_response:
                    if isinstance(item, tuple) and len(item) == 2 and item[0] == "tools":
                        tools_list.extend(item[1])

            for tool in tools_list:
                tools.append(Tool(
                    name=tool.name,
                    description=tool.description,
                    input_schema=tool.inputSchema,
                    server_name=self.name
                ))

            logger.info(f"Listed {len(tools)} tools from server {self.name}")
            return tools
            
        except Exception as e:
            logger.error(f"Error listing tools for server {self.name}: {e}")
            return []

    async def execute_tool(self, tool_name: str, arguments: dict[str, Any]) -> Any:
        """Execute a tool with given arguments"""
        if not self.session:
            raise RuntimeError(f"Server {self.name} not initialized")

        try:
            logger.info(f"Executing tool {tool_name} with args: {arguments}")
            result = await asyncio.wait_for(
                self.session.call_tool(tool_name, arguments),
                timeout=60.0
            )

            # Handle response size management
            result = self._handle_response_size(tool_name, result)

            logger.info(f"Tool {tool_name} executed successfully")
            return result
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            raise

    def _handle_response_size(self, tool_name: str, result: Any) -> Any:
        """Handle large tool responses with truncation and optimization suggestions"""
        try:
            # Handle MCP CallToolResult objects
            if hasattr(result, 'content'):
                # Extract content from MCP result
                if hasattr(result.content[0], 'text'):
                    result_data = result.content[0].text
                else:
                    result_data = str(result.content[0])
            else:
                result_data = result

            # Convert result to string to check size
            try:
                result_str = json.dumps(result_data) if not isinstance(result_data, str) else result_data
            except (TypeError, ValueError):
                # If JSON serialization fails, convert to string
                result_str = str(result_data)

            result_size = len(result_str)

            # Enhanced size limits - more generous for pricing queries
            base_size_limit = getattr(self.config, 'response_size_limit', 15000)

            # Increase limit for pricing tools
            if 'pricing' in tool_name.lower() or 'cost' in tool_name.lower():
                size_limit = base_size_limit * 3  # 45KB for pricing data
            else:
                size_limit = base_size_limit

            if result_size > size_limit:
                logger.warning(f"Tool {tool_name} returned large response ({result_size:,} chars)")

                # Check if it's a pricing tool result_too_large error
                if isinstance(result, dict) and result.get('error_type') == 'result_too_large':
                    # For pricing queries, try to extract useful information from the error
                    if 'pricing' in tool_name.lower() or 'cost' in tool_name.lower():
                        if 'sample_records' in str(result):
                            logger.info("Pricing tool returned sample data despite size error")
                            return result  # Let the model work with sample data
                    return result

                # Progressive truncation based on content type
                if 'pricing' in tool_name.lower() or 'cost' in tool_name.lower():
                    # For pricing queries, keep much more data and focus on pricing information
                    truncate_size = min(25000, size_limit // 2)

                    # Try to preserve pricing-specific content
                    if 'pricePerUnit' in result_str or 'USD' in result_str:
                        # Find pricing sections and prioritize them
                        pricing_sections = []
                        lines = result_str.split('\n')
                        for i, line in enumerate(lines):
                            if 'pricePerUnit' in line or 'USD' in line or 'pricing' in line.lower():
                                # Include context around pricing lines
                                start = max(0, i-5)
                                end = min(len(lines), i+10)
                                pricing_sections.extend(lines[start:end])

                        if pricing_sections:
                            truncated_data = '\n'.join(pricing_sections[:truncate_size//50])  # Rough line limit
                        else:
                            truncated_data = result_str[:truncate_size]
                    else:
                        truncated_data = result_str[:truncate_size]
                else:
                    # For other queries, more aggressive truncation
                    truncate_size = min(5000, size_limit // 3)
                    truncated_data = result_str[:truncate_size]

                # For other large responses, truncate and add suggestions
                truncated_result = {
                    "status": "truncated",
                    "original_size": result_size,
                    "message": f"Response truncated ({result_size:,} chars). Using first {truncate_size} chars with pricing data prioritized." if 'pricing' in tool_name.lower() else f"Response truncated ({result_size:,} chars).",
                    "truncated_data": truncated_data + "...",
                    "optimization_note": "Pricing data has been preserved where possible" if 'pricing' in tool_name.lower() else "Consider refining your query for more specific results"
                }

                if tool_name == "get_pricing":
                    truncated_result["suggestions"].extend([
                        'Use output_options={"pricing_terms": ["OnDemand"]} to reduce response size',
                        "Add more specific filters like instanceType, location, or storageClass",
                        "Use max_results parameter to limit the number of results"
                    ])

                return truncated_result

            return result

        except Exception as e:
            logger.warning(f"Error handling response size for {tool_name}: {e}")
            return result

    async def cleanup(self):
        """Clean up server resources"""
        try:
            if self._initialized:
                self._initialized = False
                self.session = None
                await asyncio.wait_for(self.exit_stack.aclose(), timeout=5.0)
                logger.info(f"Server {self.name} cleaned up")
        except Exception as e:
            logger.warning(f"Cleanup error for {self.name}: {e}")

# ------------------------
# Bedrock Client with Complete Response
# ------------------------
class BedrockClient:
    def __init__(self, model_id: str, region: str, aws_profile: str, config: Configuration = None):
        self.model_id = model_id
        self.region = region
        self.config = config

        try:
            if aws_profile == "default":
                self.session = boto3.Session()
            else:
                self.session = boto3.Session(profile_name=aws_profile)

            # Initialize both bedrock and bedrock-runtime clients
            self.bedrock_runtime = self.session.client("bedrock-runtime", region_name=region)
            self.bedrock = self.session.client("bedrock", region_name=region)
            logger.info(f"Initialized Bedrock client with model {model_id} in {region}")
        except Exception as e:
            logger.error(f"Failed to initialize Bedrock client: {e}")
            raise

    def get_fallback_model(self) -> str:
        """Get a fallback model for the current region"""
        if not self.config or self.region not in self.config.fallback_models:
            return "anthropic.claude-3-sonnet-20240229-v1:0"  # Default fallback

        fallback_models = self.config.fallback_models[self.region]
        # Return the first available fallback model that's different from current
        for model in fallback_models:
            if model != self.model_id:
                return model

        return fallback_models[0] if fallback_models else "anthropic.claude-3-sonnet-20240229-v1:0"

    async def test_model_access(self, model_id: str) -> bool:
        """Test if we can access a specific model"""
        try:
            # Try a simple converse call with minimal parameters
            test_messages = [{"role": "user", "content": [{"text": "test"}]}]
            self.bedrock_runtime.converse(
                modelId=model_id,
                messages=test_messages,
                inferenceConfig={"maxTokens": 10, "temperature": 0.1}
            )
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == 'AccessDeniedException':
                logger.warning(f"Access denied for model {model_id}: {e.response['Error']['Message']}")
                return False
            else:
                logger.warning(f"Error testing model {model_id}: {e}")
                return False
        except Exception as e:
            logger.warning(f"Unexpected error testing model {model_id}: {e}")
            return False

    async def converse_with_tools(
        self,
        messages: List[Dict[str, Any]],
        tools: List[Dict[str, Any]],
        tool_executor,
        max_tokens: int = 4000,
        temperature: float = 0.1,
        max_tool_iterations: int = 5
    ) -> tuple[str, List[Dict[str, Any]], Dict[str, Any]]:
        """Converse with automatic tool execution and return complete response"""

        # Diagnostic logging for model access
        logger.info(f"Attempting to use Bedrock model: {self.model_id} in region: {self.region}")
        try:
            # Test model access by listing available models
            models_response = self.bedrock.list_foundation_models()
            available_models = [model['modelId'] for model in models_response.get('modelSummaries', [])]
            logger.info(f"Available models in region {self.region}: {len(available_models)} models found")

            if self.model_id not in available_models:
                logger.warning(f"Model {self.model_id} not found in available models for region {self.region}")
                # Try to find similar models
                similar_models = [m for m in available_models if self.model_id.split('.')[0] in m]
                if similar_models:
                    logger.info(f"Similar available models: {similar_models}")
                else:
                    logger.warning("No similar models found in this region")
            else:
                logger.info(f"Model {self.model_id} is available in region {self.region}")
        except Exception as e:
            logger.warning(f"Could not list foundation models: {e}")

        current_messages = messages.copy()
        iterations = 0
        tool_executions = []
        final_response = ""
        usage_info = {}

        consecutive_failures = 0

        while iterations < max_tool_iterations:
            try:
                response = self.bedrock_runtime.converse(
                    modelId=self.model_id,
                    messages=current_messages,
                    inferenceConfig={
                        "maxTokens": max_tokens,
                        "temperature": temperature,
                    },
                    toolConfig={"tools": tools} if tools else {},
                )

                # Extract usage information
                if 'usage' in response:
                    usage_info = response['usage']

                # Process the response
                message = response['output']['message']
                content = message.get('content', [])

                # Check for text content
                text_content = ""
                tool_use_blocks = []

                for content_block in content:
                    if 'text' in content_block:
                        text_content += content_block['text']
                    elif 'toolUse' in content_block:
                        tool_use_blocks.append(content_block['toolUse'])

                # ENHANCED: Smart stopping conditions with query context
                if hasattr(tool_executor, 'should_continue_tools'):
                    # Extract original query from messages for context
                    original_query = ""
                    for msg in messages:
                        if msg.get('role') == 'user' and 'content' in msg:
                            if isinstance(msg['content'], str):
                                original_query = msg['content']
                            elif isinstance(msg['content'], list) and msg['content']:
                                original_query = str(msg['content'][0])
                            break

                    if not tool_executor.should_continue_tools(iterations, tool_executions, text_content, original_query):
                        logger.info(f"Smart stopping triggered at iteration {iterations}")
                        if text_content:
                            final_response = text_content
                        break
                
                # If there are tool uses, execute them
                if tool_use_blocks:
                    # Add assistant message with tool use to conversation
                    current_messages.append({
                        "role": "assistant",
                        "content": content
                    })

                    tool_results_message = {"role": "user", "content": []}

                    # ENHANCED: Smart tool limiting based on query type
                    original_query = ""
                    for msg in messages:
                        if msg.get('role') == 'user' and 'content' in msg:
                            if isinstance(msg['content'], str):
                                original_query = msg['content']
                            elif isinstance(msg['content'], list) and msg['content']:
                                original_query = str(msg['content'][0])
                            break

                    is_pricing_query = any(keyword in original_query.lower() for keyword in
                                         ['price', 'cost', 'pricing', 'expensive', 'cheap', 'bill', 'budget'])

                    # For pricing queries, allow more tools per iteration
                    max_tools_per_iteration = 2 if is_pricing_query else 1

                    if len(tool_use_blocks) > max_tools_per_iteration:
                        logger.info(f"Limiting to {max_tools_per_iteration} tools for {'pricing' if is_pricing_query else 'general'} query")
                        tool_use_blocks = tool_use_blocks[:max_tools_per_iteration]
                    
                    for tool_block in tool_use_blocks:
                        tool_execution = {
                            "tool_name": tool_block['name'],
                            "arguments": tool_block['input'],
                            "tool_use_id": tool_block['toolUseId'],
                            "status": "pending",
                            "result": None,
                            "error": None
                        }
                        
                        try:
                            logger.info(f"Executing tool: {tool_block['name']}")
                            result = await tool_executor(
                                tool_block['name'], 
                                tool_block['input']
                            )
                            
                            # Format result for Bedrock
                            if hasattr(result, 'content') and result.content:
                                if isinstance(result.content, list) and result.content:
                                    result_text = ""
                                    for content_item in result.content:
                                        if hasattr(content_item, 'text'):
                                            result_text += content_item.text
                                        else:
                                            result_text += str(content_item)
                                else:
                                    result_text = str(result.content)
                            else:
                                result_text = str(result)
                            
                            tool_execution["status"] = "success"
                            tool_execution["result"] = result_text
                            
                            tool_results_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_block['toolUseId'],
                                    "content": [{"text": result_text}]
                                }
                            })
                            
                        except Exception as e:
                            logger.error(f"Tool execution failed for {tool_block['name']}: {e}")
                            error_text = f"Error executing {tool_block['name']}: {str(e)}"
                            
                            tool_execution["status"] = "error"
                            tool_execution["error"] = str(e)
                            
                            tool_results_message["content"].append({
                                "toolResult": {
                                    "toolUseId": tool_block['toolUseId'],
                                    "content": [{"text": error_text}],
                                    "status": "error"
                                }
                            })
                        
                        tool_executions.append(tool_execution)

                    # ENHANCED: Intelligent data sufficiency check
                    if hasattr(tool_executor, '_has_sufficient_data'):
                        if tool_executor._has_sufficient_data(tool_executions):
                            # For pricing queries, be more careful about stopping
                            if is_pricing_query and hasattr(tool_executor, '_has_pricing_data_needing_analysis'):
                                if tool_executor._has_pricing_data_needing_analysis(tool_executions):
                                    logger.info("Pricing data needs analysis, continuing...")
                                else:
                                    logger.info("Sufficient pricing data obtained, stopping tool execution")
                                    current_messages.append(tool_results_message)
                                    iterations += 1
                                    continue
                            else:
                                logger.info("Sufficient data obtained, stopping tool execution")
                                current_messages.append(tool_results_message)
                                iterations += 1
                                continue

                    # Enhanced failure handling - more lenient for pricing queries
                    current_iteration_failures = sum(1 for exec in tool_executions[-len(tool_use_blocks):]
                                                    if exec["status"] == "error" and
                                                    'result_too_large' not in str(exec.get("result", "")))

                    if current_iteration_failures == len(tool_use_blocks):
                        consecutive_failures += 1
                        logger.warning(f"Tools failed in iteration {iterations}. Consecutive failures: {consecutive_failures}")

                        # Be more lenient with pricing queries
                        failure_threshold = 3 if is_pricing_query else 2

                        if consecutive_failures >= failure_threshold:
                            logger.info(f"Stopping due to {consecutive_failures} consecutive failures")
                            if is_pricing_query:
                                final_response = "I encountered some challenges retrieving the complete pricing data. "
                                final_response += "This might be due to the complexity of the pricing structure. "
                                final_response += "Could you try asking for specific pricing components or a particular region/service?"
                            else:
                                final_response = "I'm having trouble accessing the specific data you requested. "
                                final_response += "Could you please rephrase your question or provide more specific parameters?"
                            break
                    else:
                        consecutive_failures = 0  # Reset on any success

                    # Add tool results to conversation
                    current_messages.append(tool_results_message)
                    iterations += 1

                    # Continue the conversation with tool results
                    continue
                    
                else:
                    # No tool use, this is the final response
                    final_response = text_content
                    
                    # Add the final assistant response to the original messages list
                    # This ensures the conversation history is updated with the final response
                    messages.append({
                        "role": "assistant",
                        "content": [{"text": final_response}]
                    })
                    
                    break
                    
            except ClientError as e:
                if e.response['Error']['Code'] == 'AccessDeniedException':
                    logger.warning(f"Access denied for model {self.model_id}. Attempting fallback model.")

                    # Try fallback model
                    fallback_model = self.get_fallback_model()
                    if fallback_model != self.model_id:
                        logger.info(f"Trying fallback model: {fallback_model}")
                        try:
                            # Test fallback model access
                            if await self.test_model_access(fallback_model):
                                logger.info(f"Fallback model {fallback_model} is accessible. Switching to it.")
                                self.model_id = fallback_model
                                # Retry the conversation with fallback model
                                continue
                            else:
                                logger.error(f"Fallback model {fallback_model} also inaccessible")
                        except Exception as fallback_e:
                            logger.error(f"Error testing fallback model {fallback_model}: {fallback_e}")

                    final_response = f"I apologize, but I don't have access to the required AI model. Please check your AWS Bedrock permissions and model availability in region {self.region}."
                else:
                    logger.error(f"Client error in Bedrock conversation iteration {iterations}: {e}")
                    final_response = f"I apologize, but I encountered an AWS error while processing your request: {e.response['Error']['Message']}"

                # Add error response to conversation history
                messages.append({
                    "role": "assistant",
                    "content": [{"text": final_response}]
                })

                break
            except Exception as e:
                logger.error(f"Unexpected error in Bedrock conversation iteration {iterations}: {e}")
                final_response = f"I apologize, but I encountered an unexpected error while processing your request: {str(e)}"

                # Add error response to conversation history
                messages.append({
                    "role": "assistant",
                    "content": [{"text": final_response}]
                })

                break
        
        if iterations >= max_tool_iterations:
            final_response += f"\n\nNote: Maximum tool execution iterations ({max_tool_iterations}) reached."
        
        return final_response, tool_executions, usage_info

# ------------------------
# Bedrock Session Manager
# ------------------------
class BedrockSessionManager:
    """Manages AWS Bedrock sessions with fallback to in-memory storage"""

    def __init__(self, config: Configuration):
        self.config = config
        self.bedrock_agent_runtime = None
        self.fallback_sessions = {}  # In-memory fallback
        self.session_enabled = False

        if config.enable_bedrock_sessions:
            try:
                if config.aws_profile == "default":
                    session = boto3.Session()
                else:
                    session = boto3.Session(profile_name=config.aws_profile)

                # Use bedrock-agent-runtime as recommended by AWS docs
                self.bedrock_agent_runtime = session.client(
                    'bedrock-agent-runtime',
                    region_name=config.region
                )

                # Test connection with a simple call
                try:
                    # This will validate our credentials and permissions
                    test_session = self.bedrock_agent_runtime.create_session()
                    test_session_id = test_session.get('sessionId') or test_session.get('sessionIdentifier')
                    # Clean up test session - try both parameter names
                    try:
                        self.bedrock_agent_runtime.delete_session(sessionId=test_session_id)
                    except:
                        self.bedrock_agent_runtime.delete_session(sessionIdentifier=test_session_id)
                    logger.info("Bedrock session management enabled and tested successfully")
                except Exception as test_error:
                    logger.warning(f"Bedrock session test failed, but continuing: {test_error}")

                self.session_enabled = True
            except Exception as e:
                logger.warning(f"Failed to initialize Bedrock sessions, using fallback: {e}")
                self.session_enabled = False
        else:
            logger.info("Bedrock session management disabled, using in-memory storage")

    async def create_session(self, session_id: str, initial_context: Optional[Dict] = None) -> Dict:
        """Create a new session with AWS context"""
        session_data = {
            "sessionId": session_id,
            "sessionState": {
                "sessionAttributes": {
                    "awsRegion": self.config.region,
                    "awsProfile": self.config.aws_profile,
                    "createdAt": datetime.now().isoformat(),
                    "messageCount": 0,
                    "costOptimizationContext": {},
                    "activeServices": [],
                    "lastPricingQuery": None,
                    "budgetContext": {},
                    **(initial_context or {})
                }
            }
        }

        if self.session_enabled:
            try:
                # Use the AWS recommended approach: let Bedrock create the session ID
                # or use our provided session_id
                if hasattr(self.bedrock_agent_runtime, 'create_session'):
                    # Let Bedrock generate the session ID (AWS requirement)
                    response = self.bedrock_agent_runtime.create_session()

                    # Map our session_id to the Bedrock-generated one
                    bedrock_session_id = response.get('sessionId') or response.get('sessionIdentifier')
                    if bedrock_session_id:
                        # Store the mapping
                        self.fallback_sessions[f"{session_id}_bedrock_mapping"] = bedrock_session_id
                        # Store session data with our session_id for consistency
                        response['sessionId'] = session_id
                        response['bedrock_session_id'] = bedrock_session_id
                        logger.info(f"Mapped session {session_id} to Bedrock session {bedrock_session_id}")
                    else:
                        raise Exception("No session ID returned from Bedrock")
                else:
                    # If create_session doesn't exist, use fallback
                    raise Exception("create_session method not available")

                logger.info(f"Created Bedrock session: {session_id}")
                return response
            except Exception as e:
                logger.warning(f"Failed to create Bedrock session {session_id}, using fallback: {e}")
                if self.config.bedrock_session_fallback:
                    self.fallback_sessions[session_id] = session_data
                    return session_data
                else:
                    raise
        else:
            # Use fallback storage
            self.fallback_sessions[session_id] = session_data
            return session_data

    async def get_session(self, session_id: str) -> Optional[Dict]:
        """Get session data"""
        if self.session_enabled:
            try:
                # Get the Bedrock session ID from mapping
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # Use the mapped Bedrock session ID
                    response = self.bedrock_agent_runtime.get_session(sessionIdentifier=bedrock_session_id)
                    # Add our session_id for consistency
                    response['sessionId'] = session_id
                    return response
                else:
                    # No mapping found, check fallback storage
                    return self.fallback_sessions.get(session_id)

            except ClientError as e:
                if e.response['Error']['Code'] == 'ResourceNotFoundException':
                    # Check fallback storage
                    return self.fallback_sessions.get(session_id)
                else:
                    logger.warning(f"Error getting Bedrock session {session_id}: {e}")
                    if self.config.bedrock_session_fallback:
                        return self.fallback_sessions.get(session_id)
                    else:
                        raise
            except Exception as e:
                logger.warning(f"Unexpected error getting session {session_id}: {e}")
                if self.config.bedrock_session_fallback:
                    return self.fallback_sessions.get(session_id)
                else:
                    raise
        else:
            return self.fallback_sessions.get(session_id)

    async def update_session(self, session_id: str, session_metadata: Dict) -> Dict:
        """Update session metadata"""
        if self.session_enabled:
            try:
                # Get the Bedrock session ID from mapping
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # Serialize complex objects to JSON strings for Bedrock API
                    serialized_metadata = {}
                    for key, value in session_metadata.items():
                        if isinstance(value, (dict, list)):
                            serialized_metadata[key] = json.dumps(value)
                        else:
                            serialized_metadata[key] = str(value)

                    # Use the mapped Bedrock session ID with correct parameter name
                    response = self.bedrock_agent_runtime.update_session(
                        sessionIdentifier=bedrock_session_id,
                        sessionMetadata=serialized_metadata
                    )
                    response['sessionId'] = session_id  # Keep our session_id for consistency
                    return response
                else:
                    # No mapping found, update fallback storage
                    if session_id in self.fallback_sessions:
                        self.fallback_sessions[session_id]["sessionState"].update(session_metadata)
                    return self.fallback_sessions.get(session_id, {})

            except Exception as e:
                logger.warning(f"Failed to update Bedrock session {session_id}: {e}")
                if self.config.bedrock_session_fallback:
                    if session_id in self.fallback_sessions:
                        self.fallback_sessions[session_id]["sessionState"].update(session_metadata)
                    return self.fallback_sessions.get(session_id, {})
                else:
                    raise
        else:
            # Update fallback storage
            if session_id in self.fallback_sessions:
                self.fallback_sessions[session_id]["sessionState"].update(session_metadata)
            return self.fallback_sessions.get(session_id, {})

    async def delete_session(self, session_id: str) -> bool:
        """Delete a session"""
        success = True

        if self.session_enabled:
            try:
                # Get the Bedrock session ID from mapping
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # Use the mapped Bedrock session ID
                    self.bedrock_agent_runtime.delete_session(sessionIdentifier=bedrock_session_id)
                    logger.info(f"Deleted Bedrock session: {session_id} (mapped to {bedrock_session_id})")
                    # Remove the mapping
                    del self.fallback_sessions[f"{session_id}_bedrock_mapping"]

            except ClientError as e:
                if e.response['Error']['Code'] != 'ResourceNotFoundException':
                    logger.warning(f"Error deleting Bedrock session {session_id}: {e}")
                    success = False
            except Exception as e:
                logger.warning(f"Unexpected error deleting session {session_id}: {e}")
                success = False

        # Also remove from fallback storage
        if session_id in self.fallback_sessions:
            del self.fallback_sessions[session_id]

        return success

    async def create_invocation_checkpoint(self, session_id: str, step_data: Dict) -> str:
        """Create a checkpoint for conversation step"""
        invocation_id = str(uuid.uuid4())

        # ALWAYS store in fallback first to ensure we don't lose data
        if session_id not in self.fallback_sessions:
            self.fallback_sessions[session_id] = {"invocations": []}

        if "invocations" not in self.fallback_sessions[session_id]:
            self.fallback_sessions[session_id]["invocations"] = []

        self.fallback_sessions[session_id]["invocations"].append({
            "invocationId": invocation_id,
            "stepData": step_data,
            "timestamp": datetime.now().isoformat()
        })

        logger.debug(f"Stored checkpoint in fallback for session {session_id}: {step_data.get('role', 'unknown')}")

        # Try to also store in Bedrock if available
        if self.session_enabled:
            try:
                # Get the mapped Bedrock session ID
                bedrock_session_id = self.fallback_sessions.get(f"{session_id}_bedrock_mapping")

                if bedrock_session_id:
                    # For now, just update session metadata with checkpoint info
                    # This avoids the complex invocation API which may have parameter issues
                    checkpoint_metadata = {
                        "lastCheckpoint": invocation_id,
                        "lastCheckpointTime": datetime.now().isoformat(),
                        "checkpointType": step_data.get('role', 'unknown')
                    }

                    # Update session with checkpoint metadata
                    await self.update_session(session_id, checkpoint_metadata)
                    logger.debug(f"Updated Bedrock session with checkpoint metadata for {session_id}")

            except Exception as e:
                logger.warning(f"Failed to update Bedrock session with checkpoint (fallback used): {e}")

        return invocation_id

# ------------------------
# Chat Session
# ------------------------
class ChatSession:
    def __init__(self, bedrock: BedrockClient, config: Configuration):
        self.bedrock = bedrock
        self.config = config
        self.servers: Dict[str, Server] = {}
        self.tools: List[Tool] = []
        self.tool_to_server: Dict[str, str] = {}

        # Initialize Bedrock Session Manager
        self.session_manager = BedrockSessionManager(config)

        # Keep legacy storage for backward compatibility
        self.conversations: Dict[str, List[Dict]] = {}
        self.session_metadata: Dict[str, Dict] = {}

    async def initialize(self):
        """Initialize chat session with all MCP servers"""
        try:
            # Load server configuration
            config_data = Configuration.load_config("./servers_config.json")
            
            # Initialize servers
            initialization_tasks = []
            for name, server_config in config_data["mcpServers"].items():
                server = Server(name, server_config)
                self.servers[name] = server
                initialization_tasks.append(server.initialize())
            
            # Wait for all servers to initialize (with some fault tolerance)
            results = await asyncio.gather(*initialization_tasks, return_exceptions=True)
            
            initialized_servers = []
            for name, result in zip(config_data["mcpServers"].keys(), results):
                if isinstance(result, Exception):
                    logger.error(f"Failed to initialize server {name}: {result}")
                else:
                    initialized_servers.append(name)
            
            if not initialized_servers:
                logger.warning("No servers initialized successfully")
            
            # Get all tools from successfully initialized servers
            all_tools = []
            for server_name in initialized_servers:
                server = self.servers[server_name]
                try:
                    tools = await server.list_tools()
                    all_tools.extend(tools)
                    # Build tool mapping
                    for tool in tools:
                        self.tool_to_server[tool.name] = server.name
                except Exception as e:
                    logger.error(f"Failed to list tools for server {server_name}: {e}")

            self.tools = all_tools
            logger.info(f"Chat session initialized with {len(all_tools)} tools from {len(initialized_servers)} servers")

        except Exception as e:
            logger.error(f"Failed to initialize chat session: {e}")
            raise

    def should_continue_tools(self, iterations: int, tool_executions: list, text_content: str = "", query: str = "") -> bool:
        """Enhanced stopping conditions optimized for pricing queries"""

        # Detect if this is a pricing query
        is_pricing_query = self._is_pricing_query(query)

        # Use different iteration limits based on query type
        max_iterations = self.config.pricing_query_max_iterations if is_pricing_query else self.config.max_tool_iterations

        # Hard limit - but higher for pricing queries
        if iterations >= max_iterations:
            logger.info(f"Reached max iterations ({max_iterations}) for {'pricing' if is_pricing_query else 'general'} query")
            return False

        # For pricing queries, be more lenient about continuing
        if is_pricing_query:
            # Continue if we have pricing data but need analysis
            if self._has_pricing_data_needing_analysis(tool_executions):
                logger.info("Continuing pricing query - data needs analysis")
                return True

            # Continue if recent tools returned valid pricing data
            if self._has_recent_successful_pricing_data(tool_executions):
                logger.info("Continuing pricing query - recent successful data")
                return True

        # For non-pricing queries, use original logic
        if not is_pricing_query and text_content and len(text_content.strip()) > 100:
            return False

        # Stop if we have sufficient data for non-pricing queries
        if not is_pricing_query and len(tool_executions) >= 1:
            recent_executions = tool_executions[-1:]
            for exec in recent_executions:
                if (exec.get('status') == 'success' and
                    exec.get('result') and
                    'error' not in str(exec.get('result', '')).lower() and
                    len(str(exec.get('result', ''))) > 200):
                    return False

        return True

    def _is_pricing_query(self, query: str) -> bool:
        """Detect if query is related to pricing/cost analysis"""
        if not query:
            return False
        query_lower = query.lower()
        pricing_keywords = [
            'price', 'cost', 'pricing', 'expensive', 'cheap', 'bill', 'billing',
            'budget', 'spend', 'charges', 'fees', 'rate', 'rates', 'calculator',
            'estimate', 'estimation', 'how much', 'what does it cost', 'pricing model',
            'per hour', 'per month', 'per gb', 'per request', 'bedrock pricing',
            'ec2 pricing', 's3 pricing', 'lambda pricing', 'regional pricing',
            'compare cost', 'cost comparison', 'savings', 'optimize cost'
        ]
        return any(keyword in query_lower for keyword in pricing_keywords)

    def _has_pricing_data_needing_analysis(self, tool_executions: list) -> bool:
        """Check if we have pricing data that needs further analysis"""
        if not tool_executions:
            return False

        # Look for successful pricing tool executions with data
        for exec in tool_executions[-3:]:  # Check last 3 executions
            if (exec.get('status') == 'success' and
                exec.get('tool_name') and
                ('pricing' in exec.get('tool_name', '').lower() or 'cost' in exec.get('tool_name', '').lower())):

                result = exec.get('result', '')
                # Check if we have pricing data but it might need analysis
                if (result and
                    ('pricePerUnit' in str(result) or 'USD' in str(result) or 'pricing' in str(result).lower()) and
                    'result_too_large' not in str(result)):
                    return True
        return False

    def _has_recent_successful_pricing_data(self, tool_executions: list) -> bool:
        """Check if recent tool executions returned valid pricing data"""
        if not tool_executions:
            return False

        # Check last 2 executions for successful pricing data
        recent_executions = tool_executions[-2:]
        for exec in recent_executions:
            if (exec.get('status') == 'success' and
                exec.get('result') and
                ('USD' in str(exec.get('result', '')) or 'pricePerUnit' in str(exec.get('result', ''))) and
                'error' not in str(exec.get('result', '')).lower()):
                return True
        return False

    def _has_sufficient_data(self, tool_executions: list) -> bool:
        """Check if we have sufficient data to answer the query"""
        if not tool_executions:
            return False

        # For pricing queries, be more strict about what constitutes "sufficient"
        last_exec = tool_executions[-1]
        if (last_exec.get('status') == 'success' and
            last_exec.get('result')):

            result_str = str(last_exec.get('result', ''))

            # For pricing data, ensure we have actual pricing information
            if ('pricing' in last_exec.get('tool_name', '').lower() or
                'cost' in last_exec.get('tool_name', '').lower()):
                return ('USD' in result_str and 'pricePerUnit' in result_str and
                        len(result_str) > 200 and 'result_too_large' not in result_str)

            # For other queries, original logic
            return len(result_str) > 100

        return False

    def _preprocess_query(self, query: str) -> dict:
        """Enhanced query analysis optimized for pricing queries"""
        query_lower = query.lower()

        # Enhanced pricing detection
        pricing_keywords = [
            'price', 'cost', 'pricing', 'expensive', 'cheap', 'bill', 'billing',
            'budget', 'spend', 'charges', 'fees', 'rate', 'rates', 'calculator',
            'estimate', 'estimation', 'how much', 'what does it cost', 'pricing model',
            'per hour', 'per month', 'per gb', 'per request', 'bedrock pricing',
            'ec2 pricing', 's3 pricing', 'lambda pricing', 'regional pricing',
            'compare cost', 'cost comparison', 'savings', 'optimize cost'
        ]

        project_keywords = ['project', 'build', 'architecture', 'infrastructure', 'setup', 'deploy']
        comparison_keywords = ['compare', 'vs', 'versus', 'difference', 'better', 'cheaper']
        general_keywords = ['what is', 'explain', 'how does', 'concept', 'definition']

        is_pricing_query = any(word in query_lower for word in pricing_keywords)
        is_project_query = any(word in query_lower for word in project_keywords)
        is_comparison_query = any(word in query_lower for word in comparison_keywords)
        is_general_query = any(word in query_lower for word in general_keywords)

        # Determine complexity
        word_count = len(query.split())
        has_multiple_services = sum(1 for service in ['ec2', 's3', 'lambda', 'rds', 'bedrock', 'azure', 'gcp']
                                   if service in query_lower) > 1

        complexity = 'complex' if (word_count > 15 or has_multiple_services or
                                 (is_pricing_query and is_comparison_query)) else 'simple'

        # Determine primary query type
        if is_pricing_query and is_project_query:
            query_type = 'project_cost_estimate'
        elif is_pricing_query and is_comparison_query:
            query_type = 'pricing_comparison'
        elif is_pricing_query:
            query_type = 'pricing'
        elif is_comparison_query:
            query_type = 'comparison'
        elif is_general_query:
            query_type = 'general'
        else:
            query_type = 'technical'

        return {
            'needs_tools': is_pricing_query or is_comparison_query or is_project_query,
            'is_general': is_general_query,
            'is_pricing': is_pricing_query,
            'is_project_estimate': is_project_query and is_pricing_query,
            'query_type': query_type,
            'complexity': complexity,
            'requires_multiple_iterations': complexity == 'complex' or query_type in ['project_cost_estimate', 'pricing_comparison']
        }

    async def get_or_create_session(self, session_id: str) -> Dict:
        """Get existing session or create new one with AWS context"""
        try:
            # Try to get existing session
            session_data = await self.session_manager.get_session(session_id)

            if session_data:
                logger.info(f"Retrieved existing session: {session_id}")
                return session_data
            else:
                # Create new session with AWS context
                initial_context = {
                    "costOptimizationContext": {},
                    "activeServices": [],
                    "lastPricingQuery": None,
                    "budgetContext": {}
                }

                session_data = await self.session_manager.create_session(session_id, initial_context)
                logger.info(f"Created new session: {session_id}")
                return session_data

        except Exception as e:
            logger.error(f"Error managing session {session_id}: {e}")
            # Fallback to legacy session creation
            if session_id not in self.conversations:
                self.conversations[session_id] = []
                self.session_metadata[session_id] = {
                    "created_at": datetime.now().isoformat(),
                    "message_count": 0
                }
            return {"sessionId": session_id, "fallback": True}

    async def update_session_context(self, session_id: str, updates: Dict) -> None:
        """Update session context with new information"""
        try:
            session_data = await self.session_manager.get_session(session_id)
            if session_data:
                current_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})
                current_attributes.update(updates)

                await self.session_manager.update_session(session_id, {
                    "sessionAttributes": current_attributes
                })
                logger.debug(f"Updated session context for {session_id}")
            else:
                logger.warning(f"Session {session_id} not found, creating new session with updates")
                # Create new session with the updates
                await self.session_manager.create_session(session_id, updates)
        except Exception as e:
            logger.warning(f"Failed to update session context for {session_id}: {e}")
            # Fallback: store in memory
            if not hasattr(self, '_fallback_context'):
                self._fallback_context = {}
            if session_id not in self._fallback_context:
                self._fallback_context[session_id] = {}
            self._fallback_context[session_id].update(updates)
            logger.debug(f"Stored session context in fallback storage for {session_id}")

    def _extract_aws_services_from_tools(self, tool_executions: List[Dict]) -> List[str]:
        """Extract AWS services from tool executions"""
        services = set()
        for execution in tool_executions:
            tool_name = execution.get('tool_name', '').lower()
            if 'ec2' in tool_name:
                services.add('EC2')
            elif 's3' in tool_name:
                services.add('S3')
            elif 'rds' in tool_name:
                services.add('RDS')
            elif 'lambda' in tool_name:
                services.add('Lambda')
            elif 'pricing' in tool_name:
                services.add('Pricing')
            elif 'cost' in tool_name:
                services.add('Cost Explorer')
        return list(services)

    def _check_cost_analysis(self, tool_executions: List[Dict]) -> bool:
        """Check if cost analysis was performed"""
        for execution in tool_executions:
            tool_name = execution.get('tool_name', '').lower()
            if 'cost' in tool_name or 'pricing' in tool_name:
                return True
        return False



    def _has_sufficient_data(self, tool_executions: list) -> bool:
        """Check if we have sufficient data to answer the query"""
        if not tool_executions:
            return False

        # Check last execution for meaningful data
        last_exec = tool_executions[-1]
        if (last_exec.get('status') == 'success' and
            last_exec.get('result') and
            len(str(last_exec.get('result', ''))) > 100):
            return True

        return False



    def _get_system_prompt(self) -> str:
        """Get system prompt with available tools"""
        tool_descriptions = []
        for tool in self.tools:
            tool_descriptions.append(f"- {tool.name}: {tool.description}")

        tools_text = "\n".join(tool_descriptions) if tool_descriptions else "No tools available"

        return f"""You are an expert cloud services assistant specializing in comprehensive pricing analysis and cost optimization.

Available tools:
{tools_text}

PRICING QUERY OPTIMIZATION:
1. For pricing queries, use tools extensively to provide complete, accurate cost information
2. When analyzing pricing data, continue processing until you can provide specific costs, comparisons, and recommendations
3. For complex pricing queries (multi-service, regional comparisons, project estimates), use multiple iterations as needed
4. Always provide actual dollar amounts, not just "contact AWS for pricing"
5. Include cost optimization recommendations when relevant

TOOL USAGE STRATEGY:
- PRICING QUERIES: Use tools aggressively - get current pricing, compare regions, analyze cost components
- PROJECT COST ESTIMATES: Break down by service, provide monthly/annual estimates, suggest optimizations
- REGIONAL COMPARISONS: Query multiple regions, provide detailed cost differences
- SERVICE COMPARISONS: Get pricing for all relevant services, provide comprehensive analysis

GENERAL QUERIES: Answer directly without tools for basic concepts

RESPONSE QUALITY:
- Provide specific pricing figures with currency (USD)
- Include cost breakdowns by service component
- Suggest cost optimization strategies
- Compare pricing options (on-demand vs reserved vs spot)
- Mention regional pricing differences when relevant

Be thorough with pricing analysis - users need complete cost information to make informed decisions.
11. If the user asks a follow-up question, provide context from the conversation but focus on the new question

IMPORTANT: Answer only the user's current/latest question. Do not repeat or regenerate responses to previous questions in the conversation."""

    async def _get_aws_context_aware_system_prompt(self, session_id: str) -> str:
        """Get system prompt enhanced with AWS session context"""
        base_prompt = self._get_system_prompt()

        try:
            # Get session context
            session_data = await self.session_manager.get_session(session_id)
            if not session_data:
                return base_prompt

            session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})

            # Build context-aware additions
            aws_context = []

            if session_attributes.get('awsRegion'):
                aws_context.append(f"Current AWS Region: {session_attributes['awsRegion']}")

            if session_attributes.get('activeServices'):
                services = ', '.join(session_attributes['activeServices'])
                aws_context.append(f"Active AWS Services in this session: {services}")

            if session_attributes.get('lastPricingQuery'):
                last_query = session_attributes['lastPricingQuery']
                aws_context.append(f"Recent pricing analysis: {last_query.get('tool')} at {last_query.get('timestamp')}")

            if session_attributes.get('costOptimizationContext'):
                aws_context.append("Previous cost optimization analysis available in session context")

            if aws_context:
                context_text = "\n".join(aws_context)
                return f"""{base_prompt}

Session Context:
{context_text}

Use this context to provide more relevant and personalized recommendations."""

            return base_prompt

        except Exception as e:
            logger.warning(f"Failed to get session context for prompt: {e}")
            return base_prompt

    def _build_conversation_context(self, conversation: List[Dict], current_message: str) -> List[Dict]:
        """Build conversation context optimized for the current question"""

        # Start with system prompt
        context = [{
            "role": "user",
            "content": [{"text": self._get_system_prompt()}]
        }]

        # If there's conversation history, provide summarized context
        if len(conversation) > 1:  # More than just system message
            # Get the last few exchanges (exclude system message)
            recent_conversation = conversation[1:]  # Skip system message

            # Keep last 3 user-assistant pairs for context (max 6 messages)
            if len(recent_conversation) > 6:
                recent_conversation = recent_conversation[-6:]

            # Add recent conversation for context
            for msg in recent_conversation:
                # Only include clean user and assistant messages (no tool execution details)
                if msg["role"] == "user":
                    context.append(msg)
                elif msg["role"] == "assistant":
                    # Only include the final text response, not tool executions
                    text_content = ""
                    for content_item in msg.get("content", []):
                        if "text" in content_item:
                            text_content += content_item["text"]

                    if text_content.strip():
                        context.append({
                            "role": "assistant",
                            "content": [{"text": text_content}]
                        })

        # Add the current user message
        context.append({
            "role": "user",
            "content": [{"text": current_message}]
        })

        return context

    async def _build_aws_conversation_context(self, session_id: str, current_message: str) -> List[Dict]:
        """Build conversation context with AWS session awareness"""

        # Start with AWS context-aware system prompt
        system_prompt = await self._get_aws_context_aware_system_prompt(session_id)
        context = [{
            "role": "user",
            "content": [{"text": system_prompt}]
        }]

        # Try to get conversation history from session or fallback to legacy
        try:
            # Ensure session exists (this will create it if needed)
            await self.session_manager.get_session(session_id)

            # Check fallback storage for invocations (where conversation history is actually stored)
            fallback_session = self.session_manager.fallback_sessions.get(session_id, {})
            invocations = fallback_session.get("invocations", [])

            if invocations:
                # Build context from fallback invocations
                recent_invocations = invocations[-6:]  # Last 6 interactions

                for invocation in recent_invocations:
                    step_data = invocation.get("stepData", {})
                    if step_data.get("role") == "user":
                        context.append({
                            "role": "user",
                            "content": [{"text": step_data.get("content", "")}]
                        })
                    elif step_data.get("role") == "assistant":
                        context.append({
                            "role": "assistant",
                            "content": [{"text": step_data.get("content", "")}]
                        })
            else:
                # Fallback to legacy conversation storage
                if session_id in self.conversations:
                    conversation = self.conversations[session_id]
                    if len(conversation) > 1:
                        recent_conversation = conversation[1:][-6:]  # Last 6 messages

                        for msg in recent_conversation:
                            if msg["role"] == "user":
                                context.append(msg)
                            elif msg["role"] == "assistant":
                                text_content = ""
                                for content_item in msg.get("content", []):
                                    if "text" in content_item:
                                        text_content += content_item["text"]

                                if text_content.strip():
                                    context.append({
                                        "role": "assistant",
                                        "content": [{"text": text_content}]
                                    })
        except Exception as e:
            logger.warning(f"Failed to build AWS conversation context: {e}")
            # Fallback to legacy method
            if session_id in self.conversations:
                return self._build_conversation_context(self.conversations[session_id], current_message)

        # Add the current user message
        context.append({
            "role": "user",
            "content": [{"text": current_message}]
        })

        return context

    async def execute_tool_internal(self, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Internal tool execution for automatic tool handling"""
        try:
            if tool_name not in self.tool_to_server:
                available_tools = list(self.tool_to_server.keys())
                error_msg = f"Tool '{tool_name}' not found. Available tools: {available_tools[:5]}{'...' if len(available_tools) > 5 else ''}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error_type": "tool_not_found",
                    "message": error_msg,
                    "available_tools": available_tools
                }

            server_name = self.tool_to_server[tool_name]
            server = self.servers[server_name]

            # Check if server is still initialized
            if not server._initialized:
                logger.warning(f"Server {server_name} not initialized, attempting to reinitialize")
                try:
                    await server.initialize()
                except Exception as init_e:
                    logger.error(f"Failed to reinitialize server {server_name}: {init_e}")
                    return {
                        "status": "error",
                        "error_type": "server_unavailable",
                        "message": f"Server {server_name} is not available. Please try again later.",
                        "server": server_name,
                        "tool": tool_name
                    }

            result = await server.execute_tool(tool_name, arguments)
            return result

        except asyncio.TimeoutError:
            logger.error(f"Tool {tool_name} execution timed out")
            return {
                "status": "error",
                "error_type": "timeout",
                "message": f"Tool '{tool_name}' execution timed out. Try with simpler parameters or smaller datasets.",
                "tool": tool_name
            }
        except Exception as e:
            logger.error(f"Error executing tool {tool_name}: {e}")
            return {
                "status": "error",
                "error_type": "execution_error",
                "message": f"Tool execution failed: {str(e)}",
                "tool": tool_name,
                "error_details": str(e)
            }

    async def execute_aws_tool_with_context(self, session_id: str, tool_name: str, arguments: Dict[str, Any]) -> Any:
        """Execute AWS tool with session context enhancement"""
        try:
            # Get session context
            session_data = await self.session_manager.get_session(session_id)

            if session_data:
                session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})

                # Enhance arguments with AWS context
                enhanced_args = {
                    **arguments,
                    'aws_region': session_attributes.get('awsRegion', self.config.region),
                    'aws_profile': session_attributes.get('awsProfile', self.config.aws_profile),
                    'session_context': {
                        'active_services': session_attributes.get('activeServices', []),
                        'cost_context': session_attributes.get('costOptimizationContext', {}),
                        'budget_context': session_attributes.get('budgetContext', {})
                    }
                }
            else:
                enhanced_args = arguments

            # Execute tool
            result = await self.execute_tool_internal(tool_name, enhanced_args)

            # Update session with tool execution results
            await self._update_session_after_tool_execution(session_id, tool_name, result)

            return result

        except Exception as e:
            logger.error(f"Error executing AWS tool {tool_name}: {e}")
            raise

    async def _update_session_after_tool_execution(self, session_id: str, tool_name: str, result: Any):
        """Update session state based on tool execution results"""
        try:
            session_data = await self.session_manager.get_session(session_id)
            if not session_data:
                return

            current_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})
            updated_attributes = current_attributes.copy()

            # Update based on tool type
            if 'pricing' in tool_name.lower():
                updated_attributes['lastPricingQuery'] = {
                    'tool': tool_name,
                    'timestamp': datetime.now().isoformat(),
                    'result_summary': str(result)[:200]  # Store summary
                }

            if 'cost' in tool_name.lower():
                # Update cost optimization context
                cost_context = updated_attributes.get('costOptimizationContext', {})
                cost_context['lastAnalysis'] = {
                    'tool': tool_name,
                    'timestamp': datetime.now().isoformat()
                }
                updated_attributes['costOptimizationContext'] = cost_context

            # Track active AWS services
            active_services = set(updated_attributes.get('activeServices', []))
            if 'ec2' in tool_name.lower():
                active_services.add('EC2')
            elif 's3' in tool_name.lower():
                active_services.add('S3')
            elif 'rds' in tool_name.lower():
                active_services.add('RDS')
            elif 'lambda' in tool_name.lower():
                active_services.add('Lambda')
            elif 'pricing' in tool_name.lower():
                active_services.add('Pricing')
            elif 'cost' in tool_name.lower():
                active_services.add('Cost Explorer')

            updated_attributes['activeServices'] = list(active_services)

            # Update session
            await self.session_manager.update_session(session_id, {
                "sessionAttributes": updated_attributes
            })

        except Exception as e:
            logger.warning(f"Failed to update session after tool execution: {e}")

    async def get_response(self, message: str, session_id: str) -> ChatResponse:
        """Get complete chat response with enhanced AWS session management"""
        start_time = time.time()

        try:
            # Get or create AWS session
            session_data = await self.get_or_create_session(session_id)

            # Create checkpoint for user message
            await self.session_manager.create_invocation_checkpoint(session_id, {
                "role": "user",
                "content": message,
                "timestamp": datetime.now().isoformat(),
                "aws_context": {
                    "region": self.config.region,
                    "profile": self.config.aws_profile
                }
            })

            # Build AWS-aware conversation context
            conversation = await self._build_aws_conversation_context(session_id, message)

            # Update session metadata
            if session_data.get("fallback"):
                # Using fallback storage
                if session_id not in self.session_metadata:
                    self.session_metadata[session_id] = {
                        "created_at": datetime.now().isoformat(),
                        "message_count": 0
                    }
                self.session_metadata[session_id]["message_count"] += 1
                self.session_metadata[session_id]["last_activity"] = datetime.now().isoformat()
            else:
                # Update Bedrock session
                await self.update_session_context(session_id, {
                    "messageCount": session_data.get('sessionState', {}).get('sessionAttributes', {}).get('messageCount', 0) + 1,
                    "lastActivity": datetime.now().isoformat()
                })

            # Convert tools to Bedrock format
            bedrock_tools = [tool.to_bedrock_format() for tool in self.tools]

            # Get complete response with AWS-aware tool execution and optimizations
            response_text, tool_executions, usage_info = await self.bedrock.converse_with_tools(
                conversation,
                bedrock_tools,
                lambda tool, args: self.execute_aws_tool_with_context(session_id, tool, args),
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature,
                max_tool_iterations=self.config.max_tool_iterations  # Use optimized iteration limit
            )

            # Create checkpoint for assistant response
            await self.session_manager.create_invocation_checkpoint(session_id, {
                "role": "assistant",
                "content": response_text,
                "tool_executions": tool_executions,
                "aws_services_accessed": self._extract_aws_services_from_tools(tool_executions),
                "cost_analysis_performed": self._check_cost_analysis(tool_executions),
                "usage_info": usage_info,
                "timestamp": datetime.now().isoformat()
            })

            # Store in legacy format for backward compatibility
            if session_id not in self.conversations:
                self.conversations[session_id] = []

            self.conversations[session_id].append({
                "role": "user",
                "content": [{"text": message}]
            })

            self.conversations[session_id].append({
                "role": "assistant",
                "content": [{"text": response_text}]
            })

            processing_time = time.time() - start_time

            return ChatResponse(
                response=response_text,
                session_id=session_id,
                tool_executions=tool_executions,
                timestamp=datetime.now().isoformat(),
                tokens_used=usage_info.get('totalTokens'),
                processing_time=processing_time
            )

        except Exception as e:
            logger.error(f"Error in enhanced get_response: {e}")

            # Store error in session for debugging
            try:
                await self.session_manager.create_invocation_checkpoint(session_id, {
                    "role": "error",
                    "content": str(e),
                    "timestamp": datetime.now().isoformat()
                })
            except:
                pass  # Don't fail on error logging

            processing_time = time.time() - start_time

            # Provide specific error messages for common issues
            error_message = "I apologize, but I encountered an unexpected error while processing your request"

            if "AccessDenied" in str(e) or "UnauthorizedOperation" in str(e):
                error_message = "I encountered an AWS access permission error. Please check your AWS credentials and permissions."
            elif "InvalidParameterValue" in str(e):
                error_message = "There was an issue with the parameters provided. Please check your input and try again."
            elif "ThrottlingException" in str(e) or "TooManyRequests" in str(e):
                error_message = "AWS API rate limit exceeded. Please wait a moment and try again."
            elif "result_too_large" in str(e):
                error_message = "The query returned too much data. Please try using more specific filters or smaller date ranges."
            else:
                error_message = f"I apologize, but I encountered an unexpected error while processing your request: {str(e)}"

            return ChatResponse(
                response=error_message,
                session_id=session_id,
                tool_executions=[],
                timestamp=datetime.now().isoformat(),
                processing_time=processing_time
            )

    async def cleanup(self):
        """Clean up all resources"""
        logger.info("Starting chat session cleanup")
        cleanup_tasks = []
        for server in self.servers.values():
            cleanup_tasks.append(server.cleanup())
        
        if cleanup_tasks:
            await asyncio.gather(*cleanup_tasks, return_exceptions=True)
        
        logger.info("Chat session cleanup completed")

# ------------------------
# API Endpoints
# ------------------------
# Event handlers moved to lifespan manager above

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy" if is_initialized else "initializing",
        "initialized": is_initialized,
        "available_tools": len(chat_session.tools) if chat_session and chat_session.tools else 0,
        "active_servers": len([s for s in chat_session.servers.values() if s._initialized]) if chat_session else 0,
        "timestamp": datetime.now().isoformat(),
        "version": "1.0.0"
    }

@app.post("/chat", response_model=ChatResponse)
async def chat(request: ChatRequest):
    """Chat endpoint with complete response after tool execution"""
    global chat_session, is_initialized
    
    if not is_initialized or not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    if not request.message.strip():
        raise HTTPException(status_code=400, detail="Message cannot be empty")
    
    session_id = request.session_id or str(uuid.uuid4())

    # Apply query optimization if enabled
    if OPTIMIZATION_ENABLED:
        try:
            # First, check if query should be intercepted
            should_intercept, intercept_message, guidance = intercept_query(request.message)

            if should_intercept:
                logger.warning(f"Query intercepted: {request.message}")
                # Return the interception message directly
                return ChatResponse(
                    response=intercept_message,
                    session_id=session_id,
                    tool_executions=[],
                    timestamp=datetime.now().isoformat(),
                    tokens_used=0,
                    processing_time=0.0
                )

            # Get query recommendations
            recommendations = get_query_recommendations(request.message)
            logger.info(f"Query optimization recommendations: {recommendations}")

            # Log optimization insights
            if recommendations.get("validation_errors"):
                logger.warning(f"Query validation issues: {recommendations['validation_errors']}")

            if recommendations.get("estimated_tool_calls", 0) > 3:
                logger.info(f"Complex query detected, estimated {recommendations['estimated_tool_calls']} tool calls")

            # Add query guidance to the message for the bot
            guidance_to_add = guidance
            if recommendations.get("strategy_name") and recommendations["strategy_name"] != "Generic":
                strategy_guidance = f"\n\nQUERY GUIDANCE: {recommendations['strategy_name']} detected. "
                if "comparison" in recommendations["strategy_name"].lower():
                    strategy_guidance += "Query each item separately, then compare results. Do not use multiple filters on the same field."
                elif "regional" in recommendations["strategy_name"].lower():
                    strategy_guidance += "Query regions individually. Use specific filters to reduce response size."

                guidance_to_add += strategy_guidance

            if guidance_to_add:
                # Modify the message to include guidance
                original_message = request.message
                request.message = original_message + guidance_to_add
                logger.info(f"Added query guidance to message")

        except Exception as e:
            logger.warning(f"Query optimization failed: {e}")

    try:
        # OPTIMIZATION: Preprocess query to determine if tools are needed
        if hasattr(chat_session, '_preprocess_query'):
            query_analysis = chat_session._preprocess_query(request.message)
            logger.info(f"Query analysis: {query_analysis}")

            # For general queries, try to answer without tools first
            if query_analysis.get('is_general') and not query_analysis.get('needs_tools'):
                logger.info("Attempting to answer general query without tools")

        response = await chat_session.get_response(request.message, session_id)
        return response
    except Exception as e:
        logger.error(f"Error in chat endpoint: {e}")
        raise HTTPException(
            status_code=500, 
            detail=ErrorResponse(
                error="Internal server error",
                details=str(e),
                timestamp=datetime.now().isoformat()
            ).model_dump()
        )

@app.get("/sessions/{session_id}/history")
async def get_session_history(session_id: str):
    """Get conversation history for a session with AWS context"""
    global chat_session

    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")

    try:
        # Try to get from Bedrock session first
        session_data = await chat_session.session_manager.get_session(session_id)

        # Always check fallback storage for invocations (where conversation history is stored)
        fallback_session = chat_session.session_manager.fallback_sessions.get(session_id, {})
        invocations = fallback_session.get("invocations", [])

        messages = []
        for invocation in invocations:
            step_data = invocation.get("stepData", {})
            if step_data.get("role") in ["user", "assistant"]:
                messages.append({
                    "role": step_data.get("role"),
                    "content": step_data.get("content", ""),
                    "timestamp": step_data.get("timestamp"),
                    "tool_executions": step_data.get("tool_executions", []) if step_data.get("role") == "assistant" else None
                })

        # Determine source based on session data and Bedrock mapping
        bedrock_mapping = chat_session.session_manager.fallback_sessions.get(f"{session_id}_bedrock_mapping")
        if session_data and session_data.get('sessionState'):
            source = "bedrock_session"
            session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})
        elif bedrock_mapping:
            source = "bedrock_session_with_fallback"
            session_attributes = {}
        elif session_data and session_data.get('fallback'):
            source = "fallback_session"
            session_attributes = {}
        else:
            source = "unknown"
            session_attributes = {}

        # If no messages found, check legacy storage as final fallback
        if not messages and session_id in chat_session.conversations:
            return {
                "session_id": session_id,
                "messages": chat_session.conversations[session_id],
                "metadata": chat_session.session_metadata.get(session_id, {}),
                "source": "legacy_storage"
            }

        return {
            "session_id": session_id,
            "messages": messages,
            "session_attributes": session_attributes,
            "source": source,
            "bedrock_session_id": bedrock_mapping,
            "total_invocations": len(invocations)
        }

    except Exception as e:
        logger.error(f"Error getting session history: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving session history: {str(e)}")

@app.get("/sessions/{session_id}/aws-context")
async def get_session_aws_context(session_id: str):
    """Get AWS-specific context for a session"""
    global chat_session

    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")

    try:
        session_data = await chat_session.session_manager.get_session(session_id)

        if not session_data:
            raise HTTPException(status_code=404, detail="Session not found")

        session_attributes = session_data.get('sessionState', {}).get('sessionAttributes', {})

        return {
            "session_id": session_id,
            "aws_region": session_attributes.get('awsRegion'),
            "aws_profile": session_attributes.get('awsProfile'),
            "active_services": session_attributes.get('activeServices', []),
            "cost_optimization_context": session_attributes.get('costOptimizationContext', {}),
            "last_pricing_query": session_attributes.get('lastPricingQuery'),
            "budget_context": session_attributes.get('budgetContext', {}),
            "message_count": session_attributes.get('messageCount', 0),
            "created_at": session_attributes.get('createdAt'),
            "last_activity": session_attributes.get('lastActivity')
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting AWS context: {e}")
        raise HTTPException(status_code=500, detail=f"Error retrieving AWS context: {str(e)}")

@app.delete("/sessions/{session_id}")
async def delete_session(session_id: str):
    """Delete a conversation session from both Bedrock and legacy storage"""
    global chat_session

    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")

    try:
        # Delete from Bedrock session storage
        bedrock_deleted = await chat_session.session_manager.delete_session(session_id)

        # Delete from legacy storage
        legacy_deleted = False
        if session_id in chat_session.conversations:
            del chat_session.conversations[session_id]
            legacy_deleted = True

        if session_id in chat_session.session_metadata:
            del chat_session.session_metadata[session_id]
            legacy_deleted = True

        if bedrock_deleted or legacy_deleted:
            return {
                "message": f"Session {session_id} deleted",
                "bedrock_deleted": bedrock_deleted,
                "legacy_deleted": legacy_deleted
            }
        else:
            raise HTTPException(status_code=404, detail="Session not found")

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting session: {e}")
        raise HTTPException(status_code=500, detail=f"Error deleting session: {str(e)}")

@app.get("/tools")
async def list_available_tools():
    """List all available tools"""
    global chat_session
    
    if not chat_session:
        raise HTTPException(status_code=503, detail="Service not initialized")
    
    tools_info = []
    for tool in chat_session.tools:
        tools_info.append({
            "name": tool.name,
            "description": tool.description,
            "server": tool.server_name,
            "input_schema": tool.input_schema
        })
    
    return {
        "tools": tools_info,
        "total_tools": len(tools_info),
        "servers": list(chat_session.servers.keys())
    }

# ------------------------
# Main Application Entry Point
# ------------------------
if __name__ == "__main__":
    # Ensure environment is set up
    load_dotenv()
    
    # Configuration validation
    required_env_vars = ["BEDROCK_MODEL_ID"]
    missing_vars = [var for var in required_env_vars if not os.getenv(var)]
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        exit(1)
    
    # Run the application
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=int(os.getenv("PORT", "8000")),
        reload=os.getenv("ENV", "production") == "development",
        log_level=os.getenv("LOG_LEVEL", "info").lower(),
        workers=1  # Single worker for MCP connections
    )